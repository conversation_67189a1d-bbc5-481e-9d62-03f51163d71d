import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  CheckCircle,
  ClipboardCheck,
  Shield,
  Plus,
  Search,
  Edit,
  Eye,
  AlertTriangle,
  Clock,
  FileText,
  Users,
  Calendar,
  TrendingUp,
  XCircle
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { QualityCheck, NonConformance } from '@/types';

// Mock data for quality checks
const qualityChecks: QualityCheck[] = [
  {
    id: '1',
    workOrderId: 'WO-2025-001',
    checkpointName: 'Cell Voltage Verification',
    checkpointType: 'in_process',
    inspector: 'Alice Johnson',
    scheduledDate: '2025-01-21',
    completedDate: '2025-01-21',
    status: 'passed',
    criteria: [
      {
        id: '1',
        parameter: 'Cell Voltage',
        specification: '3.6V - 3.8V',
        tolerance: '±0.1V',
        testMethod: 'Digital Multimeter',
        required: true
      },
      {
        id: '2',
        parameter: 'Cell Balance',
        specification: '< 50mV difference',
        tolerance: '±10mV',
        testMethod: 'BMS Reading',
        required: true
      }
    ],
    results: [
      {
        id: '1',
        criteriaId: '1',
        measuredValue: '3.7V',
        result: 'pass',
        notes: 'All cells within specification'
      },
      {
        id: '2',
        criteriaId: '2',
        measuredValue: '35mV',
        result: 'pass',
        notes: 'Good cell balance'
      }
    ],
    notes: 'All voltage checks passed successfully'
  },
  {
    id: '2',
    batchId: 'BATCH-2025-005',
    checkpointName: 'Final Assembly Inspection',
    checkpointType: 'final',
    inspector: 'Bob Smith',
    scheduledDate: '2025-01-22',
    status: 'pending',
    criteria: [
      {
        id: '3',
        parameter: 'Physical Appearance',
        specification: 'No visible defects',
        tolerance: 'Visual inspection',
        testMethod: 'Visual Check',
        required: true
      }
    ]
  },
  {
    id: '3',
    workOrderId: 'WO-2025-002',
    checkpointName: 'Incoming Material Inspection',
    checkpointType: 'incoming',
    inspector: 'Carol Davis',
    scheduledDate: '2025-01-20',
    completedDate: '2025-01-20',
    status: 'failed',
    criteria: [
      {
        id: '4',
        parameter: 'Material Purity',
        specification: '> 99.5%',
        tolerance: '±0.1%',
        testMethod: 'Chemical Analysis',
        required: true
      }
    ],
    results: [
      {
        id: '3',
        criteriaId: '4',
        measuredValue: '99.2%',
        result: 'fail',
        notes: 'Below specification - rejected batch'
      }
    ],
    corrective_actions: [
      {
        id: '1',
        description: 'Contact supplier for replacement batch',
        assignedTo: 'Procurement Team',
        dueDate: '2025-01-25',
        status: 'in_progress'
      }
    ]
  }
];

// Mock data for non-conformances
const nonConformances: NonConformance[] = [
  {
    id: '1',
    ncNumber: 'NC-2025-001',
    workOrderId: 'WO-2025-001',
    productId: 'PM-48V-100AH-LI',
    description: 'Battery case cracking observed during assembly',
    severity: 'major',
    category: 'material',
    detectedBy: 'Production Operator',
    detectedDate: '2025-01-20',
    status: 'investigating',
    rootCause: 'Material stress during handling',
    correctiveActions: [
      {
        id: '1',
        description: 'Review handling procedures',
        assignedTo: 'Quality Manager',
        dueDate: '2025-01-25',
        status: 'in_progress'
      },
      {
        id: '2',
        description: 'Inspect remaining inventory',
        assignedTo: 'QC Inspector',
        dueDate: '2025-01-23',
        status: 'open'
      }
    ]
  },
  {
    id: '2',
    ncNumber: 'NC-2025-002',
    batchId: 'BATCH-2025-003',
    productId: 'EC-12V-200AH-LA',
    description: 'Electrolyte specific gravity out of specification',
    severity: 'critical',
    category: 'process',
    detectedBy: 'QC Inspector',
    detectedDate: '2025-01-19',
    status: 'corrective_action',
    rootCause: 'Mixing ratio error in electrolyte preparation',
    correctiveActions: [
      {
        id: '3',
        description: 'Recalibrate mixing equipment',
        assignedTo: 'Maintenance Team',
        dueDate: '2025-01-22',
        status: 'completed',
        completedDate: '2025-01-21'
      }
    ]
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'in_progress':
      return 'bg-blue-100 text-blue-800';
    case 'passed':
      return 'bg-green-100 text-green-800';
    case 'failed':
      return 'bg-red-100 text-red-800';
    case 'on_hold':
      return 'bg-orange-100 text-orange-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case 'minor':
      return 'bg-yellow-100 text-yellow-800';
    case 'major':
      return 'bg-orange-100 text-orange-800';
    case 'critical':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const QualityAssurance = () => {
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('inspections');

  const statuses = ['all', 'pending', 'in_progress', 'passed', 'failed', 'on_hold'];

  const filteredQualityChecks = qualityChecks.filter(check => {
    const matchesSearch = check.checkpointName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         check.inspector.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || check.status === selectedStatus;

    return matchesSearch && matchesStatus;
  });

  const filteredNonConformances = nonConformances.filter(nc => {
    const matchesSearch = nc.ncNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         nc.description.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  // Calculate summary statistics
  const totalInspections = qualityChecks.length;
  const pendingInspections = qualityChecks.filter(c => c.status === 'pending').length;
  const passedInspections = qualityChecks.filter(c => c.status === 'passed').length;
  const failedInspections = qualityChecks.filter(c => c.status === 'failed').length;
  const openNonConformances = nonConformances.filter(nc => nc.status !== 'closed').length;

  return (
    <Layout>
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1">Quality Assurance</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage quality control processes and ensure battery manufacturing standards.
            </p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Schedule Inspection
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Schedule Quality Inspection</DialogTitle>
                <DialogDescription>
                  Create a new quality inspection checkpoint.
                </DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="checkpoint-name">Checkpoint Name</Label>
                  <Input placeholder="Enter checkpoint name" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="checkpoint-type">Checkpoint Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="incoming">Incoming</SelectItem>
                      <SelectItem value="in_process">In Process</SelectItem>
                      <SelectItem value="final">Final</SelectItem>
                      <SelectItem value="outgoing">Outgoing</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="inspector">Inspector</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select inspector" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="alice">Alice Johnson</SelectItem>
                      <SelectItem value="bob">Bob Smith</SelectItem>
                      <SelectItem value="carol">Carol Davis</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="scheduled-date">Scheduled Date</Label>
                  <Input type="date" />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea placeholder="Enter inspection notes..." />
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsCreateDialogOpen(false)}>
                  Schedule Inspection
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Inspections</p>
                <p className="text-2xl font-bold">{totalInspections}</p>
              </div>
              <ClipboardCheck className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{pendingInspections}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Passed</p>
                <p className="text-2xl font-bold text-green-600">{passedInspections}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Failed</p>
                <p className="text-2xl font-bold text-red-600">{failedInspections}</p>
              </div>
              <XCircle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Open NCs</p>
                <p className="text-2xl font-bold text-orange-600">{openNonConformances}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search inspections or non-conformances..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status === 'all' ? 'All Status' : status.replace('_', ' ').toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for different views */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="inspections">Quality Inspections</TabsTrigger>
          <TabsTrigger value="nonconformances">Non-Conformances</TabsTrigger>
        </TabsList>

        <TabsContent value="inspections" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Quality Inspections ({filteredQualityChecks.length})</CardTitle>
              <CardDescription>
                Manage quality control checkpoints and inspections
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Checkpoint</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Inspector</TableHead>
                      <TableHead>Scheduled Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Work Order/Batch</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredQualityChecks.map((check) => (
                      <TableRow key={check.id}>
                        <TableCell>
                          <div className="font-medium">{check.checkpointName}</div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {check.checkpointType.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Users className="w-4 h-4 text-muted-foreground" />
                            {check.inspector}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4 text-muted-foreground" />
                            {new Date(check.scheduledDate).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(check.status)}>
                            {check.status.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {check.workOrderId && (
                              <div>WO: {check.workOrderId}</div>
                            )}
                            {check.batchId && (
                              <div>Batch: {check.batchId}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="icon">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <Edit className="w-4 h-4" />
                            </Button>
                            {check.status === 'pending' && (
                              <Button variant="ghost" size="icon" className="text-green-600">
                                <CheckCircle className="w-4 h-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="nonconformances" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Non-Conformances ({filteredNonConformances.length})</CardTitle>
              <CardDescription>
                Track and manage non-conforming products and corrective actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>NC Number</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Severity</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Detected By</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredNonConformances.map((nc) => (
                      <TableRow key={nc.id}>
                        <TableCell>
                          <div className="font-medium">{nc.ncNumber}</div>
                        </TableCell>
                        <TableCell>
                          <div className="max-w-xs truncate" title={nc.description}>
                            {nc.description}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getSeverityColor(nc.severity)}>
                            {nc.severity.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {nc.category.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(nc.status)}>
                            {nc.status.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Users className="w-4 h-4 text-muted-foreground" />
                            {nc.detectedBy}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4 text-muted-foreground" />
                            {new Date(nc.detectedDate).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="icon">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <FileText className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </Layout>
  );
};

export default QualityAssurance;
