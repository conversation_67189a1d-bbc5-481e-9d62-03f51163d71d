import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Package, 
  Plus, 
  Search, 
  Filter, 
  AlertTriangle, 
  TrendingDown, 
  TrendingUp,
  Edit,
  Eye
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

// Mock data for raw materials
const rawMaterials = [
  {
    id: 1,
    name: 'Lithium Carbonate',
    category: 'Active Materials',
    currentStock: 850,
    unit: 'kg',
    minimumStock: 500,
    maximumStock: 2000,
    unitCost: 45.50,
    supplier: 'ChemCorp Ltd',
    location: 'Warehouse A-1',
    lastUpdated: '2025-01-15',
    status: 'adequate'
  },
  {
    id: 2,
    name: 'Cobalt Oxide',
    category: 'Active Materials',
    currentStock: 320,
    unit: 'kg',
    minimumStock: 200,
    maximumStock: 1000,
    unitCost: 125.75,
    supplier: 'MetalSource Inc',
    location: 'Warehouse A-2',
    lastUpdated: '2025-01-14',
    status: 'adequate'
  },
  {
    id: 3,
    name: 'Nickel Sulfate',
    category: 'Active Materials',
    currentStock: 180,
    unit: 'kg',
    minimumStock: 300,
    maximumStock: 800,
    unitCost: 89.25,
    supplier: 'NiChem Solutions',
    location: 'Warehouse A-3',
    lastUpdated: '2025-01-13',
    status: 'low'
  },
  {
    id: 4,
    name: 'Electrolyte Solution',
    category: 'Electrolytes',
    currentStock: 1200,
    unit: 'L',
    minimumStock: 800,
    maximumStock: 3000,
    unitCost: 12.30,
    supplier: 'ElectroFluid Corp',
    location: 'Warehouse B-1',
    lastUpdated: '2025-01-15',
    status: 'adequate'
  },
  {
    id: 5,
    name: 'Graphite Powder',
    category: 'Anode Materials',
    currentStock: 2500,
    unit: 'kg',
    minimumStock: 1000,
    maximumStock: 5000,
    unitCost: 8.75,
    supplier: 'Carbon Materials Ltd',
    location: 'Warehouse C-1',
    lastUpdated: '2025-01-12',
    status: 'overstocked'
  },
  {
    id: 6,
    name: 'Aluminum Foil',
    category: 'Current Collectors',
    currentStock: 450,
    unit: 'm²',
    minimumStock: 200,
    maximumStock: 1000,
    unitCost: 3.25,
    supplier: 'MetalFoil Industries',
    location: 'Warehouse D-1',
    lastUpdated: '2025-01-14',
    status: 'adequate'
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'low':
      return 'bg-red-100 text-red-800';
    case 'adequate':
      return 'bg-green-100 text-green-800';
    case 'overstocked':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'low':
      return <TrendingDown size={14} />;
    case 'overstocked':
      return <TrendingUp size={14} />;
    default:
      return null;
  }
};

const RawMaterials = () => {
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const categories = ['all', ...Array.from(new Set(rawMaterials.map(m => m.category)))];
  const statuses = ['all', 'low', 'adequate', 'overstocked'];

  const filteredMaterials = rawMaterials.filter(material => {
    const matchesSearch = material.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         material.supplier.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || material.category === selectedCategory;
    const matchesStatus = selectedStatus === 'all' || material.status === selectedStatus;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const lowStockCount = rawMaterials.filter(m => m.status === 'low').length;
  const totalValue = rawMaterials.reduce((sum, m) => sum + (m.currentStock * m.unitCost), 0);

  return (
    <Layout>
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">
          Raw Materials Inventory
        </h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Manage and track raw materials for battery manufacturing.
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Package className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Materials</p>
                <p className="text-2xl font-bold">{rawMaterials.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-8 h-8 text-red-600" />
              <div>
                <p className="text-sm text-muted-foreground">Low Stock Items</p>
                <p className="text-2xl font-bold text-red-600">{lowStockCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <TrendingUp className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold">${totalValue.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Package className="w-8 h-8 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Categories</p>
                <p className="text-2xl font-bold">{categories.length - 1}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search materials or suppliers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  {statuses.map(status => (
                    <SelectItem key={status} value={status}>
                      {status === 'all' ? 'All Status' : status.charAt(0).toUpperCase() + status.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Material
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Raw Material</DialogTitle>
                  <DialogDescription>
                    Add a new raw material to the inventory system.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">Name</Label>
                    <Input id="name" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="category" className="text-right">Category</Label>
                    <Select>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active Materials</SelectItem>
                        <SelectItem value="electrolytes">Electrolytes</SelectItem>
                        <SelectItem value="anode">Anode Materials</SelectItem>
                        <SelectItem value="collectors">Current Collectors</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="stock" className="text-right">Initial Stock</Label>
                    <Input id="stock" type="number" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="unit" className="text-right">Unit</Label>
                    <Input id="unit" className="col-span-3" placeholder="kg, L, m², etc." />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => setIsAddDialogOpen(false)}>
                    Add Material
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* Materials Table */}
      <Card>
        <CardHeader>
          <CardTitle>Raw Materials ({filteredMaterials.length})</CardTitle>
          <CardDescription>
            Current inventory levels and material information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Material</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Current Stock</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Unit Cost</TableHead>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMaterials.map((material) => (
                  <TableRow key={material.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{material.name}</div>
                        <div className="text-sm text-muted-foreground">
                          Min: {material.minimumStock} {material.unit}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{material.category}</TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {material.currentStock} {material.unit}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(material.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(material.status)}
                          {material.status}
                        </div>
                      </Badge>
                    </TableCell>
                    <TableCell>${material.unitCost}</TableCell>
                    <TableCell>{material.supplier}</TableCell>
                    <TableCell>{material.location}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </Layout>
  );
};

export default RawMaterials;
