import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  Calendar as CalendarIcon,
  Factory, 
  Clock,
  Play,
  Pause,
  CheckCircle,
  AlertTriangle,
  Plus,
  Filter
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { format } from 'date-fns';

// Mock data for production schedule
const productionSchedule = [
  {
    id: 1,
    workOrderId: 'WO-2025-001',
    productModel: 'PowerMax 48V 100Ah',
    productionLine: 'Line A - Lithium-Ion',
    plannedStartDate: '2025-01-16',
    plannedEndDate: '2025-01-18',
    actualStartDate: '2025-01-16',
    actualEndDate: null,
    plannedQuantity: 50,
    producedQuantity: 32,
    priority: 'high',
    status: 'in-progress',
    estimatedCompletion: 85,
    assignedTeam: 'Team Alpha',
    shift: 'Day Shift'
  },
  {
    id: 2,
    workOrderId: 'WO-2025-002',
    productModel: 'EcoCell 12V 200Ah',
    productionLine: 'Line B - Lead-Acid',
    plannedStartDate: '2025-01-17',
    plannedEndDate: '2025-01-19',
    actualStartDate: null,
    actualEndDate: null,
    plannedQuantity: 100,
    producedQuantity: 0,
    priority: 'medium',
    status: 'scheduled',
    estimatedCompletion: 0,
    assignedTeam: 'Team Beta',
    shift: 'Day Shift'
  },
  {
    id: 3,
    workOrderId: 'WO-2025-003',
    productModel: 'FlexPower 24V 150Ah',
    productionLine: 'Line C - Nickel-Metal',
    plannedStartDate: '2025-01-15',
    plannedEndDate: '2025-01-16',
    actualStartDate: '2025-01-15',
    actualEndDate: '2025-01-16',
    plannedQuantity: 25,
    producedQuantity: 25,
    priority: 'low',
    status: 'completed',
    estimatedCompletion: 100,
    assignedTeam: 'Team Gamma',
    shift: 'Night Shift'
  },
  {
    id: 4,
    workOrderId: 'WO-2025-004',
    productModel: 'UltraMax 72V 80Ah',
    productionLine: 'Line A - Lithium-Ion',
    plannedStartDate: '2025-01-19',
    plannedEndDate: '2025-01-21',
    actualStartDate: null,
    actualEndDate: null,
    plannedQuantity: 30,
    producedQuantity: 0,
    priority: 'high',
    status: 'scheduled',
    estimatedCompletion: 0,
    assignedTeam: 'Team Alpha',
    shift: 'Day Shift'
  },
  {
    id: 5,
    workOrderId: 'WO-2025-005',
    productModel: 'CompactCell 6V 300Ah',
    productionLine: 'Line B - Lead-Acid',
    plannedStartDate: '2025-01-14',
    plannedEndDate: '2025-01-15',
    actualStartDate: '2025-01-14',
    actualEndDate: null,
    plannedQuantity: 80,
    producedQuantity: 65,
    priority: 'medium',
    status: 'delayed',
    estimatedCompletion: 75,
    assignedTeam: 'Team Beta',
    shift: 'Day Shift'
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'scheduled':
      return 'bg-blue-100 text-blue-800';
    case 'in-progress':
      return 'bg-yellow-100 text-yellow-800';
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'delayed':
      return 'bg-red-100 text-red-800';
    case 'paused':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'scheduled':
      return <Clock size={14} />;
    case 'in-progress':
      return <Play size={14} />;
    case 'completed':
      return <CheckCircle size={14} />;
    case 'delayed':
      return <AlertTriangle size={14} />;
    case 'paused':
      return <Pause size={14} />;
    default:
      return null;
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'bg-red-100 text-red-800';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800';
    case 'low':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const ProductionSchedule = () => {
  const isMobile = useIsMobile();
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedLine, setSelectedLine] = useState('all');
  const [selectedDate, setSelectedDate] = useState<Date>();

  const statuses = ['all', 'scheduled', 'in-progress', 'completed', 'delayed', 'paused'];
  const productionLines = ['all', ...Array.from(new Set(productionSchedule.map(s => s.productionLine)))];

  const filteredSchedule = productionSchedule.filter(item => {
    const matchesStatus = selectedStatus === 'all' || item.status === selectedStatus;
    const matchesLine = selectedLine === 'all' || item.productionLine === selectedLine;
    const matchesDate = !selectedDate || 
      new Date(item.plannedStartDate) <= selectedDate && 
      new Date(item.plannedEndDate) >= selectedDate;
    
    return matchesStatus && matchesLine && matchesDate;
  });

  const totalOrders = productionSchedule.length;
  const inProgressOrders = productionSchedule.filter(s => s.status === 'in-progress').length;
  const completedOrders = productionSchedule.filter(s => s.status === 'completed').length;
  const delayedOrders = productionSchedule.filter(s => s.status === 'delayed').length;

  return (
    <Layout>
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">
          Production Schedule
        </h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Plan, monitor, and manage battery production schedules across all production lines.
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Factory className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Orders</p>
                <p className="text-2xl font-bold">{totalOrders}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Play className="w-8 h-8 text-yellow-600" />
              <div>
                <p className="text-sm text-muted-foreground">In Progress</p>
                <p className="text-2xl font-bold text-yellow-600">{inProgressOrders}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold text-green-600">{completedOrders}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-8 h-8 text-red-600" />
              <div>
                <p className="text-sm text-muted-foreground">Delayed</p>
                <p className="text-2xl font-bold text-red-600">{delayedOrders}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  {statuses.map(status => (
                    <SelectItem key={status} value={status}>
                      {status === 'all' ? 'All Status' : status.charAt(0).toUpperCase() + status.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={selectedLine} onValueChange={setSelectedLine}>
                <SelectTrigger className="w-full sm:w-[200px]">
                  <SelectValue placeholder="Production Line" />
                </SelectTrigger>
                <SelectContent>
                  {productionLines.map(line => (
                    <SelectItem key={line} value={line}>
                      {line === 'all' ? 'All Lines' : line}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full sm:w-[200px] justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? format(selectedDate, "PPP") : "Filter by date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              
              {selectedDate && (
                <Button variant="ghost" onClick={() => setSelectedDate(undefined)}>
                  Clear Date
                </Button>
              )}
            </div>
            
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              New Work Order
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Production Schedule Table */}
      <Card>
        <CardHeader>
          <CardTitle>Production Schedule ({filteredSchedule.length})</CardTitle>
          <CardDescription>
            Current and upcoming production orders with progress tracking
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Work Order</TableHead>
                  <TableHead>Product</TableHead>
                  <TableHead>Production Line</TableHead>
                  <TableHead>Schedule</TableHead>
                  <TableHead>Progress</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Team</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSchedule.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{item.workOrderId}</div>
                        <div className="text-sm text-muted-foreground">{item.shift}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{item.productModel}</div>
                        <div className="text-sm text-muted-foreground">
                          Qty: {item.plannedQuantity}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{item.productionLine}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>Start: {format(new Date(item.plannedStartDate), 'MMM dd')}</div>
                        <div>End: {format(new Date(item.plannedEndDate), 'MMM dd')}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm">
                          {item.producedQuantity}/{item.plannedQuantity}
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${item.estimatedCompletion}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {item.estimatedCompletion}%
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getPriorityColor(item.priority)}>
                        {item.priority}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(item.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(item.status)}
                          {item.status}
                        </div>
                      </Badge>
                    </TableCell>
                    <TableCell>{item.assignedTeam}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </Layout>
  );
};

export default ProductionSchedule;
