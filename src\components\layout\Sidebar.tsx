import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import {
  LayoutDashboard,
  Home,
  Users,
  LogOut,
  ChevronLeft,
  ChevronRight,
  X,
  Settings as SettingsIcon,
  Package,
  Factory,
  TestTube,
  ShoppingCart,
  Building,
  DollarSign,
  UserCheck,
  BarChart3,
  Battery,
  Zap,
  Cog
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { Sheet, SheetContent, SheetClose } from "@/components/ui/sheet";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";

interface SidebarProps {
  className?: string;
  isOpen?: boolean;
  onClose?: () => void;
}

const Sidebar = ({ className, isOpen = false, onClose }: SidebarProps) => {
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const { signOut } = useAuth();
  const [collapsed, setCollapsed] = useState(false);
  const [pendingOrdersCount, setPendingOrdersCount] = useState(0);
  const [pendingPaymentsCount, setPendingPaymentsCount] = useState(0);

  // Fetch pending counts on component mount
  useEffect(() => {
    const fetchPendingCounts = async () => {
      try {
        // Removed getPendingOrdersCount and getPendingPaymentsCount
      } catch (error) {
        console.error('Failed to fetch pending counts:', error);
      }
    };

    fetchPendingCounts();
  }, []);

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      toast.success('Logged out successfully');
      navigate('/login');
    } catch (error) {
      toast.error('Failed to logout. Please try again.');
    }
  };

  // Render mobile sidebar using Sheet component
  if (isMobile) {
    return (
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="left" className="p-0 w-[280px] sm:w-[320px]">
          <div className="flex flex-col h-full bg-sidebar text-sidebar-foreground">
            {/* Mobile Sidebar Header */}
            <div className="flex items-center justify-between p-4 ">
              <div className="flex items-center gap-3">
                <img
                  src="/friction.png"
                  alt="Logo"
                  className="w-10 h-10 object-cover"
                />
              </div>
              <SheetClose asChild>
                <Button variant="ghost" size="icon" className="text-muted-foreground">
                  <X size={18} />
                </Button>
              </SheetClose>
            </div>

            {/* Mobile Navigation Links */}
            <div className="flex-1 py-4 overflow-y-auto">
              <nav className="px-2 space-y-1">
                <MobileNavItem to="/" icon={<Home size={20} />} label="Dashboard" />

                {/* Inventory Management */}
                <div className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Inventory
                </div>
                <MobileNavItem to="/inventory/raw-materials" icon={<Package size={20} />} label="Raw Materials" />
                <MobileNavItem to="/inventory/components" icon={<Cog size={20} />} label="Components" />
                <MobileNavItem to="/inventory/finished-batteries" icon={<Battery size={20} />} label="Finished Batteries" />

                {/* Production */}
                <div className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Production
                </div>
                <MobileNavItem to="/production/schedule" icon={<Factory size={20} />} label="Schedule" />
                <MobileNavItem to="/production/work-orders" icon={<LayoutDashboard size={20} />} label="Work Orders" />

                {/* Quality */}
                <div className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Quality
                </div>
                <MobileNavItem to="/quality/assurance" icon={<TestTube size={20} />} label="Quality Assurance" />
                <MobileNavItem to="/quality/testing" icon={<Zap size={20} />} label="Battery Testing" />

                {/* Sales */}
                <div className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Sales
                </div>
                <MobileNavItem to="/sales/customers" icon={<Users size={20} />} label="Customers" />
                <MobileNavItem to="/sales/orders" icon={<ShoppingCart size={20} />} label="Sales Orders" />

                {/* Other */}
                <div className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  System
                </div>
                <MobileNavItem to="/settings" icon={<SettingsIcon size={20} />} label="Settings" />
              </nav>
            </div>

            {/* Mobile Footer */}
            <div className="p-4 border-t border-border">
              <button
                onClick={handleLogout}
                className="flex items-center w-full px-3 py-2 text-sm rounded-md hover:bg-sidebar-accent hover:text-sidebar-accent-foreground justify-start gap-3"
              >
                <LogOut size={20} />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  // Desktop sidebar
  return (
    <div className={cn(
      "relative h-screen flex flex-col bg-sidebar text-sidebar-foreground border-r border-border transition-all duration-300 hidden md:flex",
      collapsed ? "w-20" : "w-64",
      className
    )}>
      {/* Logo */}
      <div className="relative flex flex-col items-center justify-center p-4 border-b border-border">
        <div className="flex items-center justify-center w-full">
          <img
            src="/friction.png"
            alt="Logo"
            className="w-10 h-10 object-cover"
            style={{ maxWidth: '40px', maxHeight: '40px' }}
          />
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className={cn("absolute -right-3 top-1/2 -translate-y-1/2 bg-background border border-border rounded-full shadow-sm text-muted-foreground", collapsed && "rotate-180")}
        >
          {collapsed ? <ChevronRight size={14} /> : <ChevronLeft size={14} />}
        </Button>
      </div>

      {/* Navigation Links */}
      <div className="flex-1 py-4 overflow-y-auto">
        <nav className="px-2 space-y-1">
          <NavItem to="/" icon={<Home size={20} />} label="Dashboard" collapsed={collapsed} />

          {/* Inventory Management */}
          {!collapsed && (
            <div className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Inventory
            </div>
          )}
          <NavItem to="/inventory/raw-materials" icon={<Package size={20} />} label="Raw Materials" collapsed={collapsed} />
          <NavItem to="/inventory/components" icon={<Cog size={20} />} label="Components" collapsed={collapsed} />
          <NavItem to="/inventory/finished-batteries" icon={<Battery size={20} />} label="Finished Batteries" collapsed={collapsed} />

          {/* Production */}
          {!collapsed && (
            <div className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Production
            </div>
          )}
          <NavItem to="/production/schedule" icon={<Factory size={20} />} label="Schedule" collapsed={collapsed} />
          <NavItem to="/production/work-orders" icon={<LayoutDashboard size={20} />} label="Work Orders" collapsed={collapsed} />

          {/* Quality */}
          {!collapsed && (
            <div className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Quality
            </div>
          )}
          <NavItem to="/quality/assurance" icon={<TestTube size={20} />} label="Quality Assurance" collapsed={collapsed} />
          <NavItem to="/quality/testing" icon={<Zap size={20} />} label="Battery Testing" collapsed={collapsed} />

          {/* Sales */}
          {!collapsed && (
            <div className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Sales
            </div>
          )}
          <NavItem to="/sales/customers" icon={<Users size={20} />} label="Customers" collapsed={collapsed} />
          <NavItem to="/sales/orders" icon={<ShoppingCart size={20} />} label="Sales Orders" collapsed={collapsed} />

          {/* System */}
          {!collapsed && (
            <div className="px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              System
            </div>
          )}
          <NavItem to="/settings" icon={<SettingsIcon size={20} />} label="Settings" collapsed={collapsed} />
        </nav>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <button
          onClick={handleLogout}
          className={cn(
            "flex items-center w-full px-3 py-2 text-sm rounded-md hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
            !collapsed && "justify-start gap-3",
            collapsed && "justify-center"
          )}
        >
          <LogOut size={20} />
          {!collapsed && <span>Logout</span>}
        </button>
      </div>
    </div>
  );
};

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  collapsed: boolean;
  count?: number;
}

const NavItem = ({ to, icon, label, collapsed, count }: NavItemProps) => (
  <Link
    to={to}
    className={cn(
      "flex items-center px-3 py-2 text-sm rounded-md transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground relative",
      to === window.location.pathname && "bg-sidebar-accent text-sidebar-accent-foreground",
      !collapsed && "justify-start gap-3",
      collapsed && "justify-center"
    )}
  >
    {icon}
    {!collapsed && (
      <div className="flex items-center w-full">
        <div className="flex items-center gap-2">
          <span>{label}</span>
          {typeof count === 'number' && count > 0 && (
            <span className="inline-flex items-center justify-center bg-red-500 text-white text-[10px] font-semibold rounded-full w-4 h-4 ml-1">
              {count}
            </span>
          )}
        </div>
      </div>
    )}
    {collapsed && typeof count === 'number' && count > 0 && (
      <span className="absolute -top-0.5 -right-0.5 bg-red-500 text-white text-[8px] px-1 py-0.5 rounded-full min-w-[12px] text-center font-medium">
        {count}
      </span>
    )}
  </Link>
);

// Mobile navigation item component
interface MobileNavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  count?: number;
}

const MobileNavItem = ({ to, icon, label, count }: MobileNavItemProps) => (
  <Link
    to={to}
    className={cn(
      "flex items-center px-3 py-2 text-sm rounded-md transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground relative",
      to === window.location.pathname && "bg-sidebar-accent text-sidebar-accent-foreground",
      "justify-start gap-3"
    )}
  >
    {icon}
    <div className="flex items-center w-full">
      <div className="flex items-center gap-2">
        <span>{label}</span>
        {typeof count === 'number' && count > 0 && (
          <span className="inline-flex items-center justify-center bg-red-500 text-white text-[10px] font-semibold rounded-full w-4 h-4 ml-1">
            {count}
          </span>
        )}
      </div>
    </div>
  </Link>
);

export default Sidebar;


