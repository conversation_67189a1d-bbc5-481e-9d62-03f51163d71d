import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import {
  ClipboardList,
  Factory,
  Settings,
  Plus,
  Search,
  Edit,
  Eye,
  Play,
  Pause,
  CheckCircle,
  Clock,
  AlertTriangle,
  Users,
  Package,
  Calendar,
  BarChart3
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { WorkOrder } from '@/types';

// Mock data for work orders
const workOrders: WorkOrder[] = [
  {
    id: '1',
    workOrderNumber: 'WO-2025-001',
    productId: 'PM-48V-100AH-LI',
    productName: 'PowerMax 48V 100Ah Lithium-Ion Battery',
    productSku: 'PM-48V-100AH-LI',
    quantity: 50,
    priority: 'high',
    status: 'in_progress',
    scheduledStartDate: '2025-01-20',
    scheduledEndDate: '2025-01-25',
    actualStartDate: '2025-01-20',
    assignedTeam: 'Production Team A',
    assignedOperator: 'John Smith',
    productionLine: 'Line 1',
    instructions: 'Standard lithium-ion battery assembly process. Ensure proper cell balancing and BMS integration.',
    progress: 65,
    estimatedHours: 120,
    actualHours: 78,
    materialRequirements: [
      {
        id: '1',
        materialId: 'LIC-18650-3500',
        materialName: 'Lithium-Ion Cell 18650',
        requiredQuantity: 1300,
        allocatedQuantity: 1300,
        unit: 'pcs',
        status: 'allocated'
      },
      {
        id: '2',
        materialId: 'BMS-48V-100A',
        materialName: 'Battery Management System',
        requiredQuantity: 50,
        allocatedQuantity: 50,
        unit: 'pcs',
        status: 'allocated'
      }
    ],
    qualityChecks: [],
    createdBy: 'Production Manager',
    createdAt: '2025-01-18T10:00:00Z',
    updatedAt: '2025-01-20T14:30:00Z'
  },
  {
    id: '2',
    workOrderNumber: 'WO-2025-002',
    productId: 'EC-12V-200AH-LA',
    productName: 'EcoCell 12V 200Ah Lead-Acid Battery',
    productSku: 'EC-12V-200AH-LA',
    quantity: 100,
    priority: 'medium',
    status: 'scheduled',
    scheduledStartDate: '2025-01-22',
    scheduledEndDate: '2025-01-26',
    assignedTeam: 'Production Team B',
    productionLine: 'Line 2',
    instructions: 'Lead-acid battery production with enhanced electrolyte formulation.',
    progress: 0,
    estimatedHours: 80,
    materialRequirements: [],
    qualityChecks: [],
    createdBy: 'Production Manager',
    createdAt: '2025-01-19T09:00:00Z',
    updatedAt: '2025-01-19T09:00:00Z'
  },
  {
    id: '3',
    workOrderNumber: 'WO-2025-003',
    productId: 'PM-24V-50AH-LI',
    productName: 'PowerMax 24V 50Ah Lithium-Ion Battery',
    productSku: 'PM-24V-50AH-LI',
    quantity: 25,
    priority: 'urgent',
    status: 'completed',
    scheduledStartDate: '2025-01-15',
    scheduledEndDate: '2025-01-18',
    actualStartDate: '2025-01-15',
    actualEndDate: '2025-01-17',
    assignedTeam: 'Production Team A',
    assignedOperator: 'Sarah Johnson',
    productionLine: 'Line 1',
    instructions: 'Rush order for key customer. Priority handling required.',
    progress: 100,
    estimatedHours: 60,
    actualHours: 55,
    materialRequirements: [],
    qualityChecks: [],
    createdBy: 'Sales Manager',
    createdAt: '2025-01-14T16:00:00Z',
    updatedAt: '2025-01-17T18:00:00Z'
  }
];

const getStatusColor = (status: WorkOrder['status']) => {
  switch (status) {
    case 'draft':
      return 'bg-gray-100 text-gray-800';
    case 'scheduled':
      return 'bg-blue-100 text-blue-800';
    case 'in_progress':
      return 'bg-yellow-100 text-yellow-800';
    case 'on_hold':
      return 'bg-orange-100 text-orange-800';
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getPriorityColor = (priority: WorkOrder['priority']) => {
  switch (priority) {
    case 'low':
      return 'bg-green-100 text-green-800';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800';
    case 'high':
      return 'bg-orange-100 text-orange-800';
    case 'urgent':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const WorkOrders = () => {
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPriority, setSelectedPriority] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const statuses = ['all', 'draft', 'scheduled', 'in_progress', 'on_hold', 'completed', 'cancelled'];
  const priorities = ['all', 'low', 'medium', 'high', 'urgent'];

  const filteredWorkOrders = workOrders.filter(order => {
    const matchesSearch = order.workOrderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.assignedTeam?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || order.status === selectedStatus;
    const matchesPriority = selectedPriority === 'all' || order.priority === selectedPriority;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  // Calculate summary statistics
  const totalOrders = workOrders.length;
  const activeOrders = workOrders.filter(o => o.status === 'in_progress').length;
  const scheduledOrders = workOrders.filter(o => o.status === 'scheduled').length;
  const completedOrders = workOrders.filter(o => o.status === 'completed').length;

  return (
    <Layout>
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1">Work Orders</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Create, manage, and track work orders for battery production.
            </p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Create Work Order
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Work Order</DialogTitle>
                <DialogDescription>
                  Create a new work order for battery production.
                </DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="product">Product</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select product" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PM-48V-100AH-LI">PowerMax 48V 100Ah</SelectItem>
                      <SelectItem value="EC-12V-200AH-LA">EcoCell 12V 200Ah</SelectItem>
                      <SelectItem value="PM-24V-50AH-LI">PowerMax 24V 50Ah</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="quantity">Quantity</Label>
                  <Input type="number" placeholder="Enter quantity" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="production-line">Production Line</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select line" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="line-1">Line 1</SelectItem>
                      <SelectItem value="line-2">Line 2</SelectItem>
                      <SelectItem value="line-3">Line 3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="start-date">Scheduled Start Date</Label>
                  <Input type="date" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="end-date">Scheduled End Date</Label>
                  <Input type="date" />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="instructions">Production Instructions</Label>
                  <Textarea placeholder="Enter detailed production instructions..." />
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsCreateDialogOpen(false)}>
                  Create Work Order
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Orders</p>
                <p className="text-2xl font-bold">{totalOrders}</p>
              </div>
              <ClipboardList className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active</p>
                <p className="text-2xl font-bold text-yellow-600">{activeOrders}</p>
              </div>
              <Play className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Scheduled</p>
                <p className="text-2xl font-bold text-blue-600">{scheduledOrders}</p>
              </div>
              <Calendar className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold text-green-600">{completedOrders}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search work orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status === 'all' ? 'All Status' : status.replace('_', ' ').toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedPriority} onValueChange={setSelectedPriority}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                {priorities.map(priority => (
                  <SelectItem key={priority} value={priority}>
                    {priority === 'all' ? 'All Priority' : priority.toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Work Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>Work Orders ({filteredWorkOrders.length})</CardTitle>
          <CardDescription>
            Manage and track production work orders
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Work Order</TableHead>
                  <TableHead>Product</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Progress</TableHead>
                  <TableHead>Assigned Team</TableHead>
                  <TableHead>Schedule</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredWorkOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{order.workOrderNumber}</div>
                        <div className="text-sm text-muted-foreground">{order.productSku}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{order.productName}</div>
                        <div className="text-sm text-muted-foreground">
                          Line: {order.productionLine || 'Not assigned'}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{order.quantity} units</div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getPriorityColor(order.priority)}>
                        {order.priority.toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(order.status)}>
                        {order.status.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span>{order.progress}%</span>
                          <span className="text-muted-foreground">
                            {order.actualHours || 0}h / {order.estimatedHours}h
                          </span>
                        </div>
                        <Progress value={order.progress} className="h-2" />
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{order.assignedTeam || 'Not assigned'}</div>
                        {order.assignedOperator && (
                          <div className="text-sm text-muted-foreground">{order.assignedOperator}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>Start: {new Date(order.scheduledStartDate).toLocaleDateString()}</div>
                        <div>End: {new Date(order.scheduledEndDate).toLocaleDateString()}</div>
                        {order.actualStartDate && (
                          <div className="text-muted-foreground">
                            Started: {new Date(order.actualStartDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="w-4 h-4" />
                        </Button>
                        {order.status === 'scheduled' && (
                          <Button variant="ghost" size="icon" className="text-green-600">
                            <Play className="w-4 h-4" />
                          </Button>
                        )}
                        {order.status === 'in_progress' && (
                          <Button variant="ghost" size="icon" className="text-orange-600">
                            <Pause className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </Layout>
  );
};

export default WorkOrders;
