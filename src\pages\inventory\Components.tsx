import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Cpu, 
  Plus, 
  Search, 
  AlertTriangle, 
  TrendingDown, 
  TrendingUp,
  Edit,
  Eye,
  Package2,
  Zap
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

// Mock data for battery components
const components = [
  {
    id: 1,
    name: 'Lithium-Ion Cell 18650',
    category: 'Battery Cells',
    partNumber: 'LIC-18650-3500',
    currentStock: 2500,
    unit: 'pcs',
    minimumStock: 1000,
    maximumStock: 5000,
    unitCost: 4.25,
    supplier: 'CellTech Industries',
    location: 'Warehouse E-1',
    specifications: '3.7V, 3500mAh',
    lastUpdated: '2025-01-15',
    status: 'adequate'
  },
  {
    id: 2,
    name: 'Battery Management System (BMS)',
    category: 'Electronics',
    partNumber: 'BMS-48V-100A',
    currentStock: 45,
    unit: 'pcs',
    minimumStock: 20,
    maximumStock: 100,
    unitCost: 125.50,
    supplier: 'ElectroControl Ltd',
    location: 'Warehouse F-1',
    specifications: '48V, 100A, CAN Bus',
    lastUpdated: '2025-01-14',
    status: 'adequate'
  },
  {
    id: 3,
    name: 'Plastic Battery Casing',
    category: 'Enclosures',
    partNumber: 'CASE-PLT-48V',
    currentStock: 85,
    unit: 'pcs',
    minimumStock: 50,
    maximumStock: 200,
    unitCost: 15.75,
    supplier: 'PlasticWorks Inc',
    location: 'Warehouse G-1',
    specifications: 'ABS Plastic, IP65',
    lastUpdated: '2025-01-13',
    status: 'adequate'
  },
  {
    id: 4,
    name: 'Cooling Fan Assembly',
    category: 'Thermal Management',
    partNumber: 'FAN-12V-80MM',
    currentStock: 15,
    unit: 'pcs',
    minimumStock: 30,
    maximumStock: 100,
    unitCost: 22.30,
    supplier: 'CoolTech Solutions',
    location: 'Warehouse H-1',
    specifications: '12V DC, 80mm, 3000 RPM',
    lastUpdated: '2025-01-12',
    status: 'low'
  },
  {
    id: 5,
    name: 'Thermal Pad',
    category: 'Thermal Management',
    partNumber: 'TPAD-5W-2MM',
    currentStock: 500,
    unit: 'pcs',
    minimumStock: 200,
    maximumStock: 1000,
    unitCost: 1.85,
    supplier: 'ThermalMat Corp',
    location: 'Warehouse H-2',
    specifications: '5W/mK, 2mm thickness',
    lastUpdated: '2025-01-15',
    status: 'adequate'
  },
  {
    id: 6,
    name: 'Connector Assembly',
    category: 'Electrical',
    partNumber: 'CONN-XT60-PAIR',
    currentStock: 1200,
    unit: 'pairs',
    minimumStock: 500,
    maximumStock: 2000,
    unitCost: 3.45,
    supplier: 'ConnectorPro Ltd',
    location: 'Warehouse I-1',
    specifications: 'XT60, 60A rated',
    lastUpdated: '2025-01-14',
    status: 'adequate'
  },
  {
    id: 7,
    name: 'Fuse 40A',
    category: 'Safety Components',
    partNumber: 'FUSE-40A-BLADE',
    currentStock: 800,
    unit: 'pcs',
    minimumStock: 200,
    maximumStock: 1000,
    unitCost: 0.95,
    supplier: 'SafetyFirst Electronics',
    location: 'Warehouse J-1',
    specifications: '40A, Blade type',
    lastUpdated: '2025-01-13',
    status: 'overstocked'
  },
  {
    id: 8,
    name: 'Insulation Sheet',
    category: 'Insulation',
    partNumber: 'INS-KAPTON-0.1MM',
    currentStock: 25,
    unit: 'm²',
    minimumStock: 50,
    maximumStock: 200,
    unitCost: 12.60,
    supplier: 'InsulationTech Inc',
    location: 'Warehouse K-1',
    specifications: 'Kapton, 0.1mm thick',
    lastUpdated: '2025-01-11',
    status: 'low'
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'low':
      return 'bg-red-100 text-red-800';
    case 'adequate':
      return 'bg-green-100 text-green-800';
    case 'overstocked':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'low':
      return <TrendingDown size={14} />;
    case 'overstocked':
      return <TrendingUp size={14} />;
    default:
      return null;
  }
};

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'Battery Cells':
      return <Zap className="w-4 h-4" />;
    case 'Electronics':
      return <Cpu className="w-4 h-4" />;
    default:
      return <Package2 className="w-4 h-4" />;
  }
};

const Components = () => {
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const categories = ['all', ...Array.from(new Set(components.map(c => c.category)))];
  const statuses = ['all', 'low', 'adequate', 'overstocked'];

  const filteredComponents = components.filter(component => {
    const matchesSearch = component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         component.partNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         component.supplier.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || component.category === selectedCategory;
    const matchesStatus = selectedStatus === 'all' || component.status === selectedStatus;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const lowStockCount = components.filter(c => c.status === 'low').length;
  const totalValue = components.reduce((sum, c) => sum + (c.currentStock * c.unitCost), 0);

  return (
    <Layout>
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">
          Components Inventory
        </h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Manage battery components, electronics, and assembly parts.
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Package2 className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Components</p>
                <p className="text-2xl font-bold">{components.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-8 h-8 text-red-600" />
              <div>
                <p className="text-sm text-muted-foreground">Low Stock Items</p>
                <p className="text-2xl font-bold text-red-600">{lowStockCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <TrendingUp className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold">${totalValue.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Cpu className="w-8 h-8 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Categories</p>
                <p className="text-2xl font-bold">{categories.length - 1}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search components or part numbers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  {statuses.map(status => (
                    <SelectItem key={status} value={status}>
                      {status === 'all' ? 'All Status' : status.charAt(0).toUpperCase() + status.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Component
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Component</DialogTitle>
                  <DialogDescription>
                    Add a new component to the inventory system.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">Name</Label>
                    <Input id="name" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="partNumber" className="text-right">Part Number</Label>
                    <Input id="partNumber" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="category" className="text-right">Category</Label>
                    <Select>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cells">Battery Cells</SelectItem>
                        <SelectItem value="electronics">Electronics</SelectItem>
                        <SelectItem value="enclosures">Enclosures</SelectItem>
                        <SelectItem value="thermal">Thermal Management</SelectItem>
                        <SelectItem value="electrical">Electrical</SelectItem>
                        <SelectItem value="safety">Safety Components</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="stock" className="text-right">Initial Stock</Label>
                    <Input id="stock" type="number" className="col-span-3" />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => setIsAddDialogOpen(false)}>
                    Add Component
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* Components Table */}
      <Card>
        <CardHeader>
          <CardTitle>Components ({filteredComponents.length})</CardTitle>
          <CardDescription>
            Current inventory levels and component specifications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Component</TableHead>
                  <TableHead>Part Number</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Current Stock</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Unit Cost</TableHead>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredComponents.map((component) => (
                  <TableRow key={component.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium flex items-center gap-2">
                          {getCategoryIcon(component.category)}
                          {component.name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {component.specifications}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {component.partNumber}
                      </code>
                    </TableCell>
                    <TableCell>{component.category}</TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {component.currentStock} {component.unit}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Min: {component.minimumStock} {component.unit}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(component.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(component.status)}
                          {component.status}
                        </div>
                      </Badge>
                    </TableCell>
                    <TableCell>${component.unitCost}</TableCell>
                    <TableCell>{component.supplier}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </Layout>
  );
};

export default Components;
