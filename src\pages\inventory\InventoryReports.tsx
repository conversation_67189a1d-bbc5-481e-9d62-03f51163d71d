import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, FileText, TrendingUp } from 'lucide-react';

const InventoryReports = () => {
  return (
    <Layout>
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">
          Inventory Reports
        </h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Comprehensive inventory analytics and reporting for all materials and products.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart className="w-5 h-5" />
              Stock Level Analysis
            </CardTitle>
            <CardDescription>
              Analyze current stock levels across all categories
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              View detailed stock level reports, identify low stock items, and track inventory turnover rates.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Consumption Trends
            </CardTitle>
            <CardDescription>
              Track material consumption patterns over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Monitor usage patterns, forecast future needs, and optimize inventory levels.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Valuation Reports
            </CardTitle>
            <CardDescription>
              Financial valuation of inventory assets
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Calculate total inventory value, cost analysis, and financial impact assessments.
            </p>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default InventoryReports;
