import Layout from '@/components/layout/Layout';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

const Budgeting = () => {
  return (
    <Layout>
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1"> B u d g e t i n g.Trim()</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Placeholder page for  B u d g e t i n g.Trim().
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Coming Soon</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            This page is under development.
          </p>
        </CardContent>
      </Card>
    </Layout>
  );
};

export default Budgeting;
