import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import {
  Star,
  Search,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  AlertTriangle,
  Eye,
  FileText,
  Award
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

// Mock data for vendor evaluations
const vendorEvaluations = [
  {
    id: 1,
    vendorName: 'LithiumTech Solutions',
    category: 'Raw Materials',
    overallScore: 92,
    qualityScore: 95,
    deliveryScore: 88,
    priceScore: 90,
    serviceScore: 94,
    lastEvaluation: '2025-01-10',
    status: 'approved',
    riskLevel: 'low',
    certifications: ['ISO 9001', 'ISO 14001', 'OHSAS 18001'],
    totalOrders: 156,
    onTimeDelivery: 94.5,
    defectRate: 0.8
  },
  {
    id: 2,
    vendorName: 'ElectroComponents Inc',
    category: 'Electronics',
    overallScore: 87,
    qualityScore: 89,
    deliveryScore: 85,
    priceScore: 88,
    serviceScore: 86,
    lastEvaluation: '2025-01-08',
    status: 'approved',
    riskLevel: 'medium',
    certifications: ['ISO 9001', 'UL Listed'],
    totalOrders: 89,
    onTimeDelivery: 89.2,
    defectRate: 1.2
  },
  {
    id: 3,
    vendorName: 'PlasticWorks Manufacturing',
    category: 'Enclosures',
    overallScore: 78,
    qualityScore: 82,
    deliveryScore: 75,
    priceScore: 85,
    serviceScore: 70,
    lastEvaluation: '2025-01-05',
    status: 'conditional',
    riskLevel: 'medium',
    certifications: ['ISO 9001'],
    totalOrders: 45,
    onTimeDelivery: 78.3,
    defectRate: 2.1
  },
  {
    id: 4,
    vendorName: 'ThermalSolutions Corp',
    category: 'Thermal Management',
    overallScore: 95,
    qualityScore: 97,
    deliveryScore: 93,
    priceScore: 92,
    serviceScore: 98,
    lastEvaluation: '2025-01-12',
    status: 'preferred',
    riskLevel: 'low',
    certifications: ['ISO 9001', 'ISO 14001', 'TS 16949'],
    totalOrders: 234,
    onTimeDelivery: 96.8,
    defectRate: 0.3
  },
  {
    id: 5,
    vendorName: 'SafetyFirst Electronics',
    category: 'Safety Components',
    overallScore: 68,
    qualityScore: 72,
    deliveryScore: 65,
    priceScore: 75,
    serviceScore: 60,
    lastEvaluation: '2024-12-28',
    status: 'under_review',
    riskLevel: 'high',
    certifications: ['UL Listed'],
    totalOrders: 23,
    onTimeDelivery: 67.4,
    defectRate: 3.8
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'preferred':
      return 'bg-green-100 text-green-800';
    case 'approved':
      return 'bg-blue-100 text-blue-800';
    case 'conditional':
      return 'bg-yellow-100 text-yellow-800';
    case 'under_review':
      return 'bg-orange-100 text-orange-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getRiskColor = (risk: string) => {
  switch (risk) {
    case 'low':
      return 'bg-green-100 text-green-800';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800';
    case 'high':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getScoreColor = (score: number) => {
  if (score >= 90) return 'text-green-600';
  if (score >= 80) return 'text-blue-600';
  if (score >= 70) return 'text-yellow-600';
  return 'text-red-600';
};

const VendorEvaluation = () => {
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const categories = ['all', ...Array.from(new Set(vendorEvaluations.map(v => v.category)))];
  const statuses = ['all', 'preferred', 'approved', 'conditional', 'under_review', 'rejected'];

  const filteredVendors = vendorEvaluations.filter(vendor => {
    const matchesSearch = vendor.vendorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vendor.category.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || vendor.category === selectedCategory;
    const matchesStatus = selectedStatus === 'all' || vendor.status === selectedStatus;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const averageScore = Math.round(vendorEvaluations.reduce((sum, v) => sum + v.overallScore, 0) / vendorEvaluations.length);
  const preferredVendors = vendorEvaluations.filter(v => v.status === 'preferred').length;
  const highRiskVendors = vendorEvaluations.filter(v => v.riskLevel === 'high').length;

  return (
    <Layout>
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">
          Vendor Evaluation
        </h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Evaluate and monitor vendor performance for battery manufacturing suppliers.
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Star className="w-8 h-8 text-yellow-600" />
              <div>
                <p className="text-sm text-muted-foreground">Average Score</p>
                <p className="text-2xl font-bold">{averageScore}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Award className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Preferred Vendors</p>
                <p className="text-2xl font-bold text-green-600">{preferredVendors}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-8 h-8 text-red-600" />
              <div>
                <p className="text-sm text-muted-foreground">High Risk</p>
                <p className="text-2xl font-bold text-red-600">{highRiskVendors}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Vendors</p>
                <p className="text-2xl font-bold">{vendorEvaluations.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search vendors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status === 'all' ? 'All Status' : status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Vendor Evaluation Table */}
      <Card>
        <CardHeader>
          <CardTitle>Vendor Evaluations ({filteredVendors.length})</CardTitle>
          <CardDescription>
            Performance scores and risk assessment for battery manufacturing suppliers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Vendor</TableHead>
                  <TableHead>Overall Score</TableHead>
                  <TableHead>Performance Metrics</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Risk Level</TableHead>
                  <TableHead>Last Evaluation</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredVendors.map((vendor) => (
                  <TableRow key={vendor.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{vendor.vendorName}</div>
                        <div className="text-sm text-muted-foreground">{vendor.category}</div>
                        <div className="flex gap-1 mt-1">
                          {vendor.certifications.slice(0, 2).map((cert, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {cert}
                            </Badge>
                          ))}
                          {vendor.certifications.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{vendor.certifications.length - 2}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="w-16">
                          <Progress value={vendor.overallScore} className="h-2" />
                        </div>
                        <span className={`font-medium ${getScoreColor(vendor.overallScore)}`}>
                          {vendor.overallScore}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm space-y-1">
                        <div className="flex justify-between">
                          <span>Quality:</span>
                          <span className={getScoreColor(vendor.qualityScore)}>{vendor.qualityScore}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Delivery:</span>
                          <span className={getScoreColor(vendor.deliveryScore)}>{vendor.deliveryScore}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Service:</span>
                          <span className={getScoreColor(vendor.serviceScore)}>{vendor.serviceScore}%</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(vendor.status)}>
                        {vendor.status.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getRiskColor(vendor.riskLevel)}>
                        {vendor.riskLevel} risk
                      </Badge>
                    </TableCell>
                    <TableCell>{vendor.lastEvaluation}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <FileText className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </Layout>
  );
};

export default VendorEvaluation;
