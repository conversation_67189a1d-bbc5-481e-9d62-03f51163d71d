import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import StatsCard from '@/components/dashboard/StatsCard';
import { 
  Battery, 
  Factory, 
  TrendingUp, 
  AlertTriangle, 
  Users, 
  DollarSign,
  Package,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { LineChart, Line, CartesianGrid, XAxis, YAxis, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

// Mock data generators for battery manufacturing
const generateProductionData = () => {
  return Array.from({ length: 7 }, (_, i) => ({
    day: `Day ${i + 1}`,
    lithiumIon: Math.floor(Math.random() * 500) + 200,
    leadAcid: Math.floor(Math.random() * 300) + 100,
    nickelMetal: Math.floor(Math.random() * 200) + 50,
  }));
};

const generateQualityData = () => [
  { name: 'Passed', value: 92, color: '#22c55e' },
  { name: 'Failed', value: 5, color: '#ef4444' },
  { name: 'Pending', value: 3, color: '#f59e0b' },
];

const generateInventoryData = () => [
  { material: 'Lithium', current: 850, minimum: 500, unit: 'kg' },
  { material: 'Cobalt', current: 320, minimum: 200, unit: 'kg' },
  { material: 'Nickel', current: 180, minimum: 300, unit: 'kg' },
  { material: 'Electrolyte', current: 1200, minimum: 800, unit: 'L' },
];

const Dashboard = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  
  const [dashboardStats, setDashboardStats] = useState({
    totalProduction: 1247,
    dailyProduction: 156,
    qualityRate: 92.3,
    activeOrders: 23,
    revenue: 2847500,
    employeesOnShift: 45
  });

  const [productionData, setProductionData] = useState(generateProductionData());
  const [qualityData, setQualityData] = useState(generateQualityData());
  const [inventoryData, setInventoryData] = useState(generateInventoryData());

  // Recent alerts/notifications
  const recentAlerts = [
    { id: 1, type: 'warning', message: 'Nickel inventory below minimum threshold', time: '2 hours ago' },
    { id: 2, type: 'info', message: 'Production line 2 maintenance scheduled', time: '4 hours ago' },
    { id: 3, type: 'success', message: 'Quality test batch #1247 passed', time: '6 hours ago' },
  ];

  // Active production lines
  const productionLines = [
    { id: 1, name: 'Line A - Lithium-Ion', status: 'active', efficiency: 94, currentBatch: 'LI-2025-001' },
    { id: 2, name: 'Line B - Lead-Acid', status: 'active', efficiency: 87, currentBatch: 'LA-2025-003' },
    { id: 3, name: 'Line C - Nickel-Metal', status: 'maintenance', efficiency: 0, currentBatch: null },
  ];

  return (
    <Layout>
      {/* Welcome section */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">
          Battery Manufacturing Dashboard
        </h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Monitor production, quality, inventory, and business performance in real-time.
        </p>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 sm:gap-6 mb-8">
        <StatsCard
          icon={<Battery size={20} />}
          title="Total Production"
          value={dashboardStats.totalProduction}
          change={"+12%"}
          className="animate-delay-1"
          iconClassName="bg-blue-100 text-blue-600"
        />
        <StatsCard
          icon={<Factory size={20} />}
          title="Daily Production"
          value={dashboardStats.dailyProduction}
          change={"+8%"}
          className="animate-delay-2"
          iconClassName="bg-green-100 text-green-600"
        />
        <StatsCard
          icon={<CheckCircle size={20} />}
          title="Quality Rate"
          value={`${dashboardStats.qualityRate}%`}
          change={"+2.1%"}
          className="animate-delay-3"
          iconClassName="bg-emerald-100 text-emerald-600"
        />
        <StatsCard
          icon={<Package size={20} />}
          title="Active Orders"
          value={dashboardStats.activeOrders}
          change={"+5"}
          className="animate-delay-4"
          iconClassName="bg-orange-100 text-orange-600"
        />
        <StatsCard
          icon={<DollarSign size={20} />}
          title="Revenue (MTD)"
          value={`$${(dashboardStats.revenue / 1000).toFixed(0)}K`}
          change={"+15%"}
          className="animate-delay-5"
          iconClassName="bg-purple-100 text-purple-600"
        />
        <StatsCard
          icon={<Users size={20} />}
          title="On Shift"
          value={dashboardStats.employeesOnShift}
          change={null}
          className="animate-delay-6"
          iconClassName="bg-indigo-100 text-indigo-600"
        />
      </div>

      {/* Charts and detailed information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Production Chart */}
        <Card className="opacity-0 animate-fade-in">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp size={20} />
              Production Overview (Last 7 Days)
            </CardTitle>
            <CardDescription>
              Battery production by type
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={productionData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="lithiumIon" name="Lithium-Ion" fill="#3b82f6" />
                  <Bar dataKey="leadAcid" name="Lead-Acid" fill="#10b981" />
                  <Bar dataKey="nickelMetal" name="Nickel-Metal" fill="#f59e0b" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Quality Distribution */}
        <Card className="opacity-0 animate-fade-in">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle size={20} />
              Quality Control Status
            </CardTitle>
            <CardDescription>
              Current quality test results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={qualityData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}%`}
                  >
                    {qualityData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Production Lines Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card className="opacity-0 animate-fade-in">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Factory size={20} />
              Production Lines Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {productionLines.map((line) => (
                <div key={line.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{line.name}</h4>
                      <Badge 
                        variant={line.status === 'active' ? 'default' : 'secondary'}
                        className={line.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                      >
                        {line.status}
                      </Badge>
                    </div>
                    {line.currentBatch && (
                      <p className="text-sm text-muted-foreground">Batch: {line.currentBatch}</p>
                    )}
                    {line.status === 'active' && (
                      <div className="mt-2">
                        <div className="flex justify-between text-sm mb-1">
                          <span>Efficiency</span>
                          <span>{line.efficiency}%</span>
                        </div>
                        <Progress value={line.efficiency} className="h-2" />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Inventory Alerts */}
        <Card className="opacity-0 animate-fade-in">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle size={20} />
              Inventory Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {inventoryData.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{item.material}</h4>
                      {item.current < item.minimum && (
                        <Badge variant="destructive">Low Stock</Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Current: {item.current} {item.unit} | Min: {item.minimum} {item.unit}
                    </p>
                    <div className="mt-2">
                      <Progress 
                        value={(item.current / (item.minimum * 2)) * 100} 
                        className="h-2"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Alerts */}
      <Card className="opacity-0 animate-fade-in">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock size={20} />
            Recent Alerts & Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentAlerts.map((alert) => (
              <div key={alert.id} className="flex items-start gap-3 p-3 border rounded-lg">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  alert.type === 'warning' ? 'bg-yellow-500' :
                  alert.type === 'info' ? 'bg-blue-500' : 'bg-green-500'
                }`} />
                <div className="flex-1">
                  <p className="text-sm">{alert.message}</p>
                  <p className="text-xs text-muted-foreground mt-1">{alert.time}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </Layout>
  );
};

export default Dashboard;
