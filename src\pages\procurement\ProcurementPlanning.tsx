import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const ProcurementPlanning = () => {
  return (
    <Layout>
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1"> P r o c u r e m e n t P l a n n i n g.Trim()</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Placeholder page for  P r o c u r e m e n t P l a n n i n g.Trim().
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Coming Soon</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            This page is under development.
          </p>
        </CardContent>
      </Card>
    </Layout>
  );
};

export default ProcurementPlanning;
