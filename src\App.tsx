import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/hooks/use-theme";
import { AuthProvider } from "@/contexts/AuthContext";

// Core pages
import Index from "./pages/Index";
import Settings from "./pages/Settings";
import Login from "./pages/Login";
import NotFound from "./pages/NotFound";
import Unauthorized from "./pages/Unauthorized";

// Battery Manufacturing ERP Pages - will be created
// Dashboard
import Dashboard from "./pages/Dashboard";

// Inventory Management
import RawMaterials from "./pages/inventory/RawMaterials";
import Components from "./pages/inventory/Components";
import FinishedBatteries from "./pages/inventory/FinishedBatteries";
import InventoryReports from "./pages/inventory/InventoryReports";

// Production Planning & Manufacturing
import ProductionSchedule from "./pages/production/ProductionSchedule";
import WorkOrders from "./pages/production/WorkOrders";
import ManufacturingProcesses from "./pages/production/ManufacturingProcesses";
import CapacityPlanning from "./pages/production/CapacityPlanning";
import ProductionLineMonitoring from "./pages/production/ProductionLineMonitoring";

// Quality Control & Testing
import QualityAssurance from "./pages/quality/QualityAssurance";
import BatteryTesting from "./pages/quality/BatteryTesting";
import ComplianceTracking from "./pages/quality/ComplianceTracking";
import QualityReports from "./pages/quality/QualityReports";

// Sales & Customer Management
import CustomerManagement from "./pages/sales/CustomerManagement";
import SalesOrders from "./pages/sales/SalesOrders";
import Quotations from "./pages/sales/Quotations";
import DeliveryTracking from "./pages/sales/DeliveryTracking";

// Purchase Orders & Supplier Management
import SupplierManagement from "./pages/procurement/SupplierManagement";
import PurchaseOrders from "./pages/procurement/PurchaseOrders";
import VendorEvaluation from "./pages/procurement/VendorEvaluation";
import ProcurementPlanning from "./pages/procurement/ProcurementPlanning";

// Financial Management
import Accounting from "./pages/finance/Accounting";
import Invoicing from "./pages/finance/Invoicing";
import Payments from "./pages/finance/Payments";
import CostTracking from "./pages/finance/CostTracking";
import Budgeting from "./pages/finance/Budgeting";

// Human Resources
import EmployeeManagement from "./pages/hr/EmployeeManagement";
import Payroll from "./pages/hr/Payroll";
import TrainingRecords from "./pages/hr/TrainingRecords";
import SafetyCompliance from "./pages/hr/SafetyCompliance";

// Reporting & Analytics
import ProductionReports from "./pages/reports/ProductionReports";
import FinancialReports from "./pages/reports/FinancialReports";
import QualityAnalytics from "./pages/reports/QualityAnalytics";
import BusinessIntelligence from "./pages/reports/BusinessIntelligence";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <ThemeProvider defaultTheme="light">
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              {/* Authentication Routes */}
              <Route path="/login" element={<Login />} />
              <Route path="/unauthorized" element={<Unauthorized />} />

              {/* Dashboard */}
              <Route path="/" element={<Dashboard />} />

              {/* Inventory Management */}
              <Route path="/inventory/raw-materials" element={<RawMaterials />} />
              <Route path="/inventory/components" element={<Components />} />
              <Route path="/inventory/finished-batteries" element={<FinishedBatteries />} />
              <Route path="/inventory/reports" element={<InventoryReports />} />

              {/* Production Planning & Manufacturing */}
              <Route path="/production/schedule" element={<ProductionSchedule />} />
              <Route path="/production/work-orders" element={<WorkOrders />} />
              <Route path="/production/processes" element={<ManufacturingProcesses />} />
              <Route path="/production/capacity-planning" element={<CapacityPlanning />} />
              <Route path="/production/line-monitoring" element={<ProductionLineMonitoring />} />

              {/* Quality Control & Testing */}
              <Route path="/quality/assurance" element={<QualityAssurance />} />
              <Route path="/quality/testing" element={<BatteryTesting />} />
              <Route path="/quality/compliance" element={<ComplianceTracking />} />
              <Route path="/quality/reports" element={<QualityReports />} />

              {/* Sales & Customer Management */}
              <Route path="/sales/customers" element={<CustomerManagement />} />
              <Route path="/sales/orders" element={<SalesOrders />} />
              <Route path="/sales/quotations" element={<Quotations />} />
              <Route path="/sales/delivery" element={<DeliveryTracking />} />

              {/* Purchase Orders & Supplier Management */}
              <Route path="/procurement/suppliers" element={<SupplierManagement />} />
              <Route path="/procurement/purchase-orders" element={<PurchaseOrders />} />
              <Route path="/procurement/vendor-evaluation" element={<VendorEvaluation />} />
              <Route path="/procurement/planning" element={<ProcurementPlanning />} />

              {/* Financial Management */}
              <Route path="/finance/accounting" element={<Accounting />} />
              <Route path="/finance/invoicing" element={<Invoicing />} />
              <Route path="/finance/payments" element={<Payments />} />
              <Route path="/finance/cost-tracking" element={<CostTracking />} />
              <Route path="/finance/budgeting" element={<Budgeting />} />

              {/* Human Resources */}
              <Route path="/hr/employees" element={<EmployeeManagement />} />
              <Route path="/hr/payroll" element={<Payroll />} />
              <Route path="/hr/training" element={<TrainingRecords />} />
              <Route path="/hr/safety" element={<SafetyCompliance />} />

              {/* Reporting & Analytics */}
              <Route path="/reports/production" element={<ProductionReports />} />
              <Route path="/reports/financial" element={<FinancialReports />} />
              <Route path="/reports/quality" element={<QualityAnalytics />} />
              <Route path="/reports/business-intelligence" element={<BusinessIntelligence />} />

              {/* Settings */}
              <Route path="/settings" element={<Settings />} />

              {/* Catch-all route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </ThemeProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
