import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Users,
  UserPlus,
  Search,
  Filter,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Award,
  Clock,
  Edit,
  Eye,
  MoreHorizontal
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

// Mock data for employees
const employees = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Production Manager',
    department: 'Manufacturing',
    location: 'Factory Floor A',
    hireDate: '2022-03-15',
    status: 'active',
    salary: 75000,
    avatar: '/avatars/sarah.jpg',
    certifications: ['Six Sigma Green Belt', 'Safety Management'],
    workSchedule: 'Day Shift',
    emergencyContact: 'John Johnson - (*************'
  },
  {
    id: 2,
    name: 'Michael Chen',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Quality Control Engineer',
    department: 'Quality Assurance',
    location: 'QC Lab',
    hireDate: '2021-08-22',
    status: 'active',
    salary: 68000,
    avatar: '/avatars/michael.jpg',
    certifications: ['ISO 9001 Lead Auditor', 'Battery Testing Specialist'],
    workSchedule: 'Day Shift',
    emergencyContact: 'Lisa Chen - (*************'
  },
  {
    id: 3,
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'R&D Scientist',
    department: 'Research & Development',
    location: 'R&D Lab',
    hireDate: '2020-11-10',
    status: 'active',
    salary: 82000,
    avatar: '/avatars/emily.jpg',
    certifications: ['PhD Chemistry', 'Battery Technology Specialist'],
    workSchedule: 'Flexible',
    emergencyContact: 'Carlos Rodriguez - (*************'
  },
  {
    id: 4,
    name: 'David Thompson',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Maintenance Technician',
    department: 'Maintenance',
    location: 'Factory Floor B',
    hireDate: '2023-01-08',
    status: 'active',
    salary: 52000,
    avatar: '/avatars/david.jpg',
    certifications: ['Electrical Safety', 'Equipment Maintenance'],
    workSchedule: 'Night Shift',
    emergencyContact: 'Mary Thompson - (*************'
  },
  {
    id: 5,
    name: 'Lisa Wang',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Sales Manager',
    department: 'Sales',
    location: 'Office Building',
    hireDate: '2019-05-20',
    status: 'active',
    salary: 85000,
    avatar: '/avatars/lisa.jpg',
    certifications: ['Sales Leadership', 'Customer Relations'],
    workSchedule: 'Day Shift',
    emergencyContact: 'James Wang - (*************'
  },
  {
    id: 6,
    name: 'Robert Brown',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Safety Officer',
    department: 'Safety & Compliance',
    location: 'Office Building',
    hireDate: '2021-12-03',
    status: 'on_leave',
    salary: 62000,
    avatar: '/avatars/robert.jpg',
    certifications: ['OSHA 30-Hour', 'Hazmat Handling'],
    workSchedule: 'Day Shift',
    emergencyContact: 'Susan Brown - (*************'
  }
];

const departments = ['All', 'Manufacturing', 'Quality Assurance', 'Research & Development', 'Maintenance', 'Sales', 'Safety & Compliance'];
const statuses = ['All', 'active', 'on_leave', 'terminated'];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800';
    case 'on_leave':
      return 'bg-yellow-100 text-yellow-800';
    case 'terminated':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const formatSalary = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const EmployeeManagement = () => {
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('All');
  const [selectedStatus, setSelectedStatus] = useState('All');

  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.position.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDepartment = selectedDepartment === 'All' || employee.department === selectedDepartment;
    const matchesStatus = selectedStatus === 'All' || employee.status === selectedStatus;

    return matchesSearch && matchesDepartment && matchesStatus;
  });

  const totalEmployees = employees.length;
  const activeEmployees = employees.filter(e => e.status === 'active').length;
  const onLeaveEmployees = employees.filter(e => e.status === 'on_leave').length;
  const averageSalary = Math.round(employees.reduce((sum, e) => sum + e.salary, 0) / employees.length);

  return (
    <Layout>
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1">
              Employee Management
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage employee information and workforce for battery manufacturing operations.
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Button>
              <UserPlus className="w-4 h-4 mr-2" />
              Add Employee
            </Button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Users className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Employees</p>
                <p className="text-2xl font-bold">{totalEmployees}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Users className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Active</p>
                <p className="text-2xl font-bold text-green-600">{activeEmployees}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Clock className="w-8 h-8 text-yellow-600" />
              <div>
                <p className="text-sm text-muted-foreground">On Leave</p>
                <p className="text-2xl font-bold text-yellow-600">{onLeaveEmployees}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Award className="w-8 h-8 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Avg. Salary</p>
                <p className="text-2xl font-bold">{formatSalary(averageSalary)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search employees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="Department" />
              </SelectTrigger>
              <SelectContent>
                {departments.map(dept => (
                  <SelectItem key={dept} value={dept}>
                    {dept === 'All' ? 'All Departments' : dept}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status === 'All' ? 'All Status' : status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Employee Table */}
      <Card>
        <CardHeader>
          <CardTitle>Employees ({filteredEmployees.length})</CardTitle>
          <CardDescription>
            Complete employee directory and information management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Employee</TableHead>
                  <TableHead>Position</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Hire Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEmployees.map((employee) => (
                  <TableRow key={employee.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={employee.avatar} />
                          <AvatarFallback>
                            {employee.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{employee.name}</div>
                          <div className="text-sm text-muted-foreground">{employee.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{employee.position}</div>
                        <div className="text-sm text-muted-foreground">{employee.workSchedule}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div>{employee.department}</div>
                        <div className="text-sm text-muted-foreground">{employee.location}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm space-y-1">
                        <div className="flex items-center gap-1">
                          <Phone className="w-3 h-3" />
                          {employee.phone}
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {employee.location}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(employee.status)}>
                        {employee.status.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>{employee.hireDate}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </Layout>
  );
};

export default EmployeeManagement;
