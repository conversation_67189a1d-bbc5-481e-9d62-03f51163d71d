import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  ShoppingCart,
  FileText,
  TrendingUp,
  Plus,
  Search,
  Edit,
  Eye,
  Calendar,
  DollarSign,
  Package,
  Truck,
  CheckCircle,
  Clock,
  AlertTriangle,
  User,
  MapPin,
  Phone
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { SalesOrder } from '@/types';

// Mock data for sales orders
const salesOrders: SalesOrder[] = [
  {
    id: '1',
    orderNumber: 'SO-2025-001',
    customerId: '1',
    customerName: 'TechCorp Industries',
    customerEmail: '<EMAIL>',
    orderDate: '2025-01-15',
    requestedDeliveryDate: '2025-02-15',
    status: 'confirmed',
    priority: 'high',
    totalAmount: 25000,
    currency: 'USD',
    paymentTerms: 'Net 30',
    shippingAddress: {
      street: '123 Tech Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA'
    },
    billingAddress: {
      street: '123 Tech Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA'
    },
    items: [
      {
        id: '1',
        productSku: 'PM-48V-100AH-LI',
        productName: 'PowerMax 48V 100Ah Lithium Battery',
        quantity: 10,
        unitPrice: 2500,
        totalPrice: 25000,
        deliveryDate: '2025-02-15'
      }
    ],
    salesRep: 'Alice Johnson',
    notes: 'Priority order for new facility setup. Customer requires expedited delivery.',
    createdBy: 'Sales Team',
    lastUpdated: '2025-01-20T10:30:00Z'
  },
  {
    id: '2',
    orderNumber: 'SO-2025-002',
    customerId: '2',
    customerName: 'GreenEnergy Solutions',
    customerEmail: '<EMAIL>',
    orderDate: '2025-01-18',
    requestedDeliveryDate: '2025-03-01',
    status: 'in_production',
    priority: 'medium',
    totalAmount: 18500,
    currency: 'USD',
    paymentTerms: 'Net 15',
    shippingAddress: {
      street: '456 Solar Avenue',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94102',
      country: 'USA'
    },
    billingAddress: {
      street: '456 Solar Avenue',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94102',
      country: 'USA'
    },
    items: [
      {
        id: '2',
        productSku: 'EC-12V-200AH-LA',
        productName: 'EcoCell 12V 200Ah Lead Acid Battery',
        quantity: 25,
        unitPrice: 740,
        totalPrice: 18500,
        deliveryDate: '2025-03-01'
      }
    ],
    salesRep: 'Bob Smith',
    notes: 'Standard order for solar installation project.',
    createdBy: 'Sales Team',
    lastUpdated: '2025-01-21T14:15:00Z'
  },
  {
    id: '3',
    orderNumber: 'SO-2025-003',
    customerId: '3',
    customerName: 'AutoMotive Systems Ltd',
    customerEmail: '<EMAIL>',
    orderDate: '2025-01-20',
    requestedDeliveryDate: '2025-02-28',
    status: 'pending',
    priority: 'low',
    totalAmount: 12000,
    currency: 'USD',
    paymentTerms: 'Net 30',
    shippingAddress: {
      street: '789 Motor Drive',
      city: 'Detroit',
      state: 'MI',
      zipCode: '48201',
      country: 'USA'
    },
    billingAddress: {
      street: '789 Motor Drive',
      city: 'Detroit',
      state: 'MI',
      zipCode: '48201',
      country: 'USA'
    },
    items: [
      {
        id: '3',
        productSku: 'PM-24V-50AH-LI',
        productName: 'PowerMax 24V 50Ah Lithium Battery',
        quantity: 8,
        unitPrice: 1500,
        totalPrice: 12000,
        deliveryDate: '2025-02-28'
      }
    ],
    salesRep: 'Carol Davis',
    notes: 'Awaiting credit approval before processing.',
    createdBy: 'Sales Team',
    lastUpdated: '2025-01-21T09:00:00Z'
  },
  {
    id: '4',
    orderNumber: 'SO-2025-004',
    customerId: '4',
    customerName: 'PowerGrid Utilities',
    customerEmail: '<EMAIL>',
    orderDate: '2025-01-19',
    requestedDeliveryDate: '2025-04-15',
    status: 'shipped',
    priority: 'high',
    totalAmount: 85000,
    currency: 'USD',
    paymentTerms: 'Net 45',
    shippingAddress: {
      street: '321 Energy Boulevard',
      city: 'Houston',
      state: 'TX',
      zipCode: '77002',
      country: 'USA'
    },
    billingAddress: {
      street: '321 Energy Boulevard',
      city: 'Houston',
      state: 'TX',
      zipCode: '77002',
      country: 'USA'
    },
    items: [
      {
        id: '4',
        productSku: 'PM-48V-100AH-LI',
        productName: 'PowerMax 48V 100Ah Lithium Battery',
        quantity: 34,
        unitPrice: 2500,
        totalPrice: 85000,
        deliveryDate: '2025-04-15'
      }
    ],
    salesRep: 'David Wilson',
    notes: 'Large utility order. Shipped via freight carrier.',
    createdBy: 'Sales Team',
    lastUpdated: '2025-01-22T16:45:00Z'
  }
];

const getStatusColor = (status: SalesOrder['status']) => {
  switch (status) {
    case 'draft':
      return 'bg-gray-100 text-gray-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'confirmed':
      return 'bg-blue-100 text-blue-800';
    case 'in_production':
      return 'bg-purple-100 text-purple-800';
    case 'ready_to_ship':
      return 'bg-orange-100 text-orange-800';
    case 'shipped':
      return 'bg-green-100 text-green-800';
    case 'delivered':
      return 'bg-green-100 text-green-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getPriorityColor = (priority: SalesOrder['priority']) => {
  switch (priority) {
    case 'low':
      return 'bg-gray-100 text-gray-800';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800';
    case 'high':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusProgress = (status: SalesOrder['status']) => {
  switch (status) {
    case 'draft': return 10;
    case 'pending': return 20;
    case 'confirmed': return 40;
    case 'in_production': return 60;
    case 'ready_to_ship': return 80;
    case 'shipped': return 90;
    case 'delivered': return 100;
    case 'cancelled': return 0;
    default: return 0;
  }
};

const SalesOrders = () => {
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPriority, setSelectedPriority] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('orders');

  const statuses = ['all', 'draft', 'pending', 'confirmed', 'in_production', 'ready_to_ship', 'shipped', 'delivered', 'cancelled'];
  const priorities = ['all', 'low', 'medium', 'high'];

  const filteredOrders = salesOrders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.salesRep.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || order.status === selectedStatus;
    const matchesPriority = selectedPriority === 'all' || order.priority === selectedPriority;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  // Calculate summary statistics
  const totalOrders = salesOrders.length;
  const pendingOrders = salesOrders.filter(o => o.status === 'pending').length;
  const confirmedOrders = salesOrders.filter(o => o.status === 'confirmed').length;
  const inProductionOrders = salesOrders.filter(o => o.status === 'in_production').length;
  const shippedOrders = salesOrders.filter(o => o.status === 'shipped').length;
  const totalValue = salesOrders.reduce((sum, o) => sum + o.totalAmount, 0);

  return (
    <Layout>
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1">Sales Orders</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Create, manage, and track battery sales orders from quote to delivery.
            </p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Create Order
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl">
              <DialogHeader>
                <DialogTitle>Create Sales Order</DialogTitle>
                <DialogDescription>
                  Create a new sales order for battery products.
                </DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customer">Customer</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select customer" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="techcorp">TechCorp Industries</SelectItem>
                      <SelectItem value="greenenergy">GreenEnergy Solutions</SelectItem>
                      <SelectItem value="automotive">AutoMotive Systems Ltd</SelectItem>
                      <SelectItem value="powergrid">PowerGrid Utilities</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sales-rep">Sales Representative</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select sales rep" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="alice">Alice Johnson</SelectItem>
                      <SelectItem value="bob">Bob Smith</SelectItem>
                      <SelectItem value="carol">Carol Davis</SelectItem>
                      <SelectItem value="david">David Wilson</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="delivery-date">Requested Delivery Date</Label>
                  <Input type="date" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="payment-terms">Payment Terms</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select terms" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="net-15">Net 15</SelectItem>
                      <SelectItem value="net-30">Net 30</SelectItem>
                      <SelectItem value="net-45">Net 45</SelectItem>
                      <SelectItem value="cod">COD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="notes">Order Notes</Label>
                  <Textarea placeholder="Enter order notes and special instructions..." />
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsCreateDialogOpen(false)}>
                  Create Order
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Orders</p>
                <p className="text-2xl font-bold">{totalOrders}</p>
              </div>
              <ShoppingCart className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{pendingOrders}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Confirmed</p>
                <p className="text-2xl font-bold text-blue-600">{confirmedOrders}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">In Production</p>
                <p className="text-2xl font-bold text-purple-600">{inProductionOrders}</p>
              </div>
              <Package className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Shipped</p>
                <p className="text-2xl font-bold text-green-600">{shippedOrders}</p>
              </div>
              <Truck className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold text-green-600">${totalValue.toLocaleString()}</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status === 'all' ? 'All Status' : status.replace('_', ' ').toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedPriority} onValueChange={setSelectedPriority}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                {priorities.map(priority => (
                  <SelectItem key={priority} value={priority}>
                    {priority === 'all' ? 'All Priority' : priority.toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for different views */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="orders">Sales Orders</TabsTrigger>
          <TabsTrigger value="analytics">Order Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="orders" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Sales Orders ({filteredOrders.length})</CardTitle>
              <CardDescription>
                Manage and track sales orders from creation to delivery
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Order Number</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Sales Rep</TableHead>
                      <TableHead>Order Date</TableHead>
                      <TableHead>Delivery Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Total Amount</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredOrders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell>
                          <div className="font-medium">{order.orderNumber}</div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{order.customerName}</div>
                            <div className="text-sm text-muted-foreground">{order.customerEmail}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <User className="w-4 h-4 text-muted-foreground" />
                            {order.salesRep}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4 text-muted-foreground" />
                            {new Date(order.orderDate).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4 text-muted-foreground" />
                            {new Date(order.requestedDeliveryDate).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(order.status)}>
                            {order.status.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getPriorityColor(order.priority)}>
                            {order.priority.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium text-green-600">
                            ${order.totalAmount.toLocaleString()} {order.currency}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="w-20">
                            <Progress value={getStatusProgress(order.status)} className="h-2" />
                            <div className="text-xs text-muted-foreground mt-1">
                              {getStatusProgress(order.status)}%
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="icon">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <FileText className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Order Status Distribution</CardTitle>
                <CardDescription>
                  Current distribution of orders by status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {statuses.filter(s => s !== 'all').map(status => {
                    const count = salesOrders.filter(o => o.status === status).length;
                    const percentage = totalOrders > 0 ? (count / totalOrders) * 100 : 0;
                    return (
                      <div key={status} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge className={getStatusColor(status as SalesOrder['status'])}>
                            {status.replace('_', ' ').toUpperCase()}
                          </Badge>
                          <span className="text-sm">{count} orders</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Progress value={percentage} className="w-20 h-2" />
                          <span className="text-sm text-muted-foreground w-12">
                            {percentage.toFixed(0)}%
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Priority Distribution</CardTitle>
                <CardDescription>
                  Order distribution by priority level
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {priorities.filter(p => p !== 'all').map(priority => {
                    const count = salesOrders.filter(o => o.priority === priority).length;
                    const percentage = totalOrders > 0 ? (count / totalOrders) * 100 : 0;
                    return (
                      <div key={priority} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge className={getPriorityColor(priority as SalesOrder['priority'])}>
                            {priority.toUpperCase()}
                          </Badge>
                          <span className="text-sm">{count} orders</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Progress value={percentage} className="w-20 h-2" />
                          <span className="text-sm text-muted-foreground w-12">
                            {percentage.toFixed(0)}%
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Customers by Order Value</CardTitle>
                <CardDescription>
                  Customers ranked by total order value
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(
                    salesOrders.reduce((acc, order) => {
                      acc[order.customerName] = (acc[order.customerName] || 0) + order.totalAmount;
                      return acc;
                    }, {} as Record<string, number>)
                  )
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 5)
                    .map(([customer, value]) => (
                      <div key={customer} className="flex items-center justify-between">
                        <div className="font-medium">{customer}</div>
                        <div className="text-green-600 font-medium">
                          ${value.toLocaleString()}
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Sales Representatives Performance</CardTitle>
                <CardDescription>
                  Order count and value by sales rep
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(
                    salesOrders.reduce((acc, order) => {
                      if (!acc[order.salesRep]) {
                        acc[order.salesRep] = { count: 0, value: 0 };
                      }
                      acc[order.salesRep].count += 1;
                      acc[order.salesRep].value += order.totalAmount;
                      return acc;
                    }, {} as Record<string, { count: number; value: number }>)
                  )
                    .sort(([,a], [,b]) => b.value - a.value)
                    .map(([rep, stats]) => (
                      <div key={rep} className="space-y-1">
                        <div className="flex items-center justify-between">
                          <div className="font-medium">{rep}</div>
                          <div className="text-green-600 font-medium">
                            ${stats.value.toLocaleString()}
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {stats.count} orders
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </Layout>
  );
};

export default SalesOrders;
