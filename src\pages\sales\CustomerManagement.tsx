import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Users,
  Building,
  Phone,
  Plus,
  Search,
  Edit,
  Eye,
  Mail,
  MapPin,
  CreditCard,
  Calendar,
  TrendingUp,
  DollarSign,
  ShoppingCart,
  Star,
  MessageSquare
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { Customer, CustomerContact, CustomerCredit, CustomerContract } from '@/types';

// Mock data for customers
const customers: (Customer & {
  contacts: CustomerContact[],
  credit: CustomerCredit,
  contracts: CustomerContract[]
})[] = [
  {
    id: '1',
    name: 'TechCorp Industries',
    email: '<EMAIL>',
    phone: '******-0123',
    location: 'New York, NY',
    address: '123 Tech Street, New York, NY 10001',
    join_date: '2023-06-15',
    total_orders: 45,
    total_spent: 125000,
    status: 'active',
    contacts: [
      {
        id: '1',
        customerId: '1',
        name: 'John Smith',
        title: 'Procurement Manager',
        email: '<EMAIL>',
        phone: '******-0124',
        department: 'Procurement',
        isPrimary: true
      },
      {
        id: '2',
        customerId: '1',
        name: 'Sarah Johnson',
        title: 'Technical Specialist',
        email: '<EMAIL>',
        phone: '******-0125',
        department: 'Engineering',
        isPrimary: false
      }
    ],
    credit: {
      customerId: '1',
      creditLimit: 50000,
      currentBalance: 12500,
      availableCredit: 37500,
      paymentTerms: 'Net 30',
      creditRating: 'excellent',
      lastCreditReview: '2024-12-01'
    },
    contracts: [
      {
        id: '1',
        customerId: '1',
        contractNumber: 'CNT-2024-001',
        contractType: 'supply',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        value: 200000,
        currency: 'USD',
        status: 'active',
        terms: 'Annual supply agreement for lithium-ion batteries'
      }
    ]
  },
  {
    id: '2',
    name: 'GreenEnergy Solutions',
    email: '<EMAIL>',
    phone: '******-0200',
    location: 'San Francisco, CA',
    address: '456 Solar Avenue, San Francisco, CA 94102',
    join_date: '2023-09-20',
    total_orders: 28,
    total_spent: 89000,
    status: 'active',
    contacts: [
      {
        id: '3',
        customerId: '2',
        name: 'Mike Chen',
        title: 'Operations Director',
        email: '<EMAIL>',
        phone: '******-0201',
        department: 'Operations',
        isPrimary: true
      }
    ],
    credit: {
      customerId: '2',
      creditLimit: 30000,
      currentBalance: 8500,
      availableCredit: 21500,
      paymentTerms: 'Net 15',
      creditRating: 'good',
      lastCreditReview: '2024-11-15'
    },
    contracts: []
  },
  {
    id: '3',
    name: 'AutoMotive Systems Ltd',
    email: '<EMAIL>',
    phone: '******-0300',
    location: 'Detroit, MI',
    address: '789 Motor Drive, Detroit, MI 48201',
    join_date: '2024-01-10',
    total_orders: 12,
    total_spent: 45000,
    status: 'active',
    contacts: [
      {
        id: '4',
        customerId: '3',
        name: 'Lisa Rodriguez',
        title: 'Supply Chain Manager',
        email: '<EMAIL>',
        phone: '******-0301',
        department: 'Supply Chain',
        isPrimary: true
      }
    ],
    credit: {
      customerId: '3',
      creditLimit: 25000,
      currentBalance: 15000,
      availableCredit: 10000,
      paymentTerms: 'Net 30',
      creditRating: 'fair',
      lastCreditReview: '2024-10-01'
    },
    contracts: []
  },
  {
    id: '4',
    name: 'PowerGrid Utilities',
    email: '<EMAIL>',
    phone: '******-0400',
    location: 'Houston, TX',
    address: '321 Energy Boulevard, Houston, TX 77002',
    join_date: '2023-03-05',
    total_orders: 67,
    total_spent: 285000,
    status: 'active',
    contacts: [
      {
        id: '5',
        customerId: '4',
        name: 'Robert Wilson',
        title: 'Chief Procurement Officer',
        email: '<EMAIL>',
        phone: '******-0401',
        department: 'Procurement',
        isPrimary: true
      }
    ],
    credit: {
      customerId: '4',
      creditLimit: 100000,
      currentBalance: 25000,
      availableCredit: 75000,
      paymentTerms: 'Net 45',
      creditRating: 'excellent',
      lastCreditReview: '2024-12-15'
    },
    contracts: [
      {
        id: '2',
        customerId: '4',
        contractNumber: 'CNT-2024-002',
        contractType: 'framework',
        startDate: '2024-06-01',
        endDate: '2026-05-31',
        value: 500000,
        currency: 'USD',
        status: 'active',
        terms: 'Two-year framework agreement for utility-scale battery systems'
      }
    ]
  }
];

const getStatusColor = (status: Customer['status']) => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800';
    case 'inactive':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getCreditRatingColor = (rating: string) => {
  switch (rating) {
    case 'excellent':
      return 'bg-green-100 text-green-800';
    case 'good':
      return 'bg-blue-100 text-blue-800';
    case 'fair':
      return 'bg-yellow-100 text-yellow-800';
    case 'poor':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const CustomerManagement = () => {
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('customers');

  const statuses = ['all', 'active', 'inactive'];

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || customer.status === selectedStatus;

    return matchesSearch && matchesStatus;
  });

  // Calculate summary statistics
  const totalCustomers = customers.length;
  const activeCustomers = customers.filter(c => c.status === 'active').length;
  const totalRevenue = customers.reduce((sum, c) => sum + c.total_spent, 0);
  const avgOrderValue = customers.reduce((sum, c) => sum + c.total_orders, 0) > 0
    ? totalRevenue / customers.reduce((sum, c) => sum + c.total_orders, 0)
    : 0;

  return (
    <Layout>
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1">Customer Management</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage customer relationships and track battery sales opportunities.
            </p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Add Customer
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add New Customer</DialogTitle>
                <DialogDescription>
                  Create a new customer profile with contact information.
                </DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="company-name">Company Name</Label>
                  <Input placeholder="Enter company name" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input type="email" placeholder="Enter email address" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input placeholder="Enter phone number" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input placeholder="Enter location" />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="address">Address</Label>
                  <Textarea placeholder="Enter full address..." />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="credit-limit">Credit Limit</Label>
                  <Input type="number" placeholder="Enter credit limit" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="payment-terms">Payment Terms</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select terms" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="net-15">Net 15</SelectItem>
                      <SelectItem value="net-30">Net 30</SelectItem>
                      <SelectItem value="net-45">Net 45</SelectItem>
                      <SelectItem value="cod">COD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsCreateDialogOpen(false)}>
                  Add Customer
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Customers</p>
                <p className="text-2xl font-bold">{totalCustomers}</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Customers</p>
                <p className="text-2xl font-bold text-green-600">{activeCustomers}</p>
              </div>
              <Building className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold text-green-600">${totalRevenue.toLocaleString()}</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Order Value</p>
                <p className="text-2xl font-bold text-blue-600">${avgOrderValue.toFixed(0)}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status === 'all' ? 'All Status' : status.toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
