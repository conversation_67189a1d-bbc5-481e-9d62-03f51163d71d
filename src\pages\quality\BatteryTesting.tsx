import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Zap,
  TestTube,
  BarChart,
  Plus,
  Search,
  Edit,
  Eye,
  Play,
  Pause,
  CheckCircle,
  Clock,
  AlertTriangle,
  FileText,
  Award,
  TrendingUp,
  Battery,
  Thermometer,
  Activity
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { BatteryTest } from '@/types';

// Mock data for battery tests
const batteryTests: BatteryTest[] = [
  {
    id: '1',
    testNumber: 'BT-2025-001',
    batteryId: 'BAT-PM-48V-001',
    batterySku: 'PM-48V-100AH-LI',
    batchId: 'BATCH-2025-001',
    testType: 'capacity',
    testProtocol: 'IEC 61960-3 Capacity Test',
    testStandard: 'IEC 61960',
    operator: 'Test Engineer A',
    startDate: '2025-01-20T09:00:00Z',
    endDate: '2025-01-20T17:30:00Z',
    status: 'completed',
    testConditions: [
      { parameter: 'Temperature', value: '25', unit: '°C' },
      { parameter: 'Humidity', value: '45', unit: '%' },
      { parameter: 'Discharge Rate', value: '0.2', unit: 'C' }
    ],
    testResults: [
      {
        id: '1',
        parameter: 'Capacity',
        measuredValue: 98.5,
        unit: 'Ah',
        specification: '≥ 95 Ah',
        result: 'pass',
        timestamp: '2025-01-20T17:30:00Z'
      },
      {
        id: '2',
        parameter: 'Energy',
        measuredValue: 4728,
        unit: 'Wh',
        specification: '≥ 4560 Wh',
        result: 'pass',
        timestamp: '2025-01-20T17:30:00Z'
      }
    ],
    certification: {
      certificateNumber: 'CERT-2025-001',
      issuedBy: 'Internal QA Lab',
      issuedDate: '2025-01-21',
      expiryDate: '2026-01-21',
      standard: 'IEC 61960',
      status: 'valid'
    },
    notes: 'Battery performed within specifications. Capacity test completed successfully.'
  },
  {
    id: '2',
    testNumber: 'BT-2025-002',
    batteryId: 'BAT-PM-48V-002',
    batterySku: 'PM-48V-100AH-LI',
    batchId: 'BATCH-2025-001',
    testType: 'cycle_life',
    testProtocol: 'Accelerated Cycle Life Test',
    testStandard: 'IEC 61960',
    operator: 'Test Engineer B',
    startDate: '2025-01-18T08:00:00Z',
    status: 'in_progress',
    testConditions: [
      { parameter: 'Temperature', value: '45', unit: '°C' },
      { parameter: 'Charge Rate', value: '0.5', unit: 'C' },
      { parameter: 'Discharge Rate', value: '1.0', unit: 'C' }
    ],
    testResults: [
      {
        id: '3',
        parameter: 'Cycles Completed',
        measuredValue: 1250,
        unit: 'cycles',
        specification: '≥ 2000 cycles',
        result: 'warning',
        timestamp: '2025-01-21T12:00:00Z'
      }
    ],
    notes: 'Cycle life testing in progress. Currently at 1250 cycles with 85% capacity retention.'
  },
  {
    id: '3',
    testNumber: 'BT-2025-003',
    batteryId: 'BAT-EC-12V-001',
    batterySku: 'EC-12V-200AH-LA',
    batchId: 'BATCH-2025-002',
    testType: 'safety',
    testProtocol: 'UL 2054 Safety Test',
    testStandard: 'UL 2054',
    operator: 'Safety Test Engineer',
    startDate: '2025-01-19T10:00:00Z',
    status: 'scheduled',
    testConditions: [
      { parameter: 'Temperature', value: '23', unit: '°C' },
      { parameter: 'Humidity', value: '50', unit: '%' }
    ],
    testResults: [],
    notes: 'Safety testing scheduled for lead-acid battery batch.'
  },
  {
    id: '4',
    testNumber: 'BT-2025-004',
    batteryId: 'BAT-PM-24V-001',
    batterySku: 'PM-24V-50AH-LI',
    batchId: 'BATCH-2025-003',
    testType: 'performance',
    testProtocol: 'Performance Validation Test',
    testStandard: 'Internal Standard',
    operator: 'Test Engineer C',
    startDate: '2025-01-21T14:00:00Z',
    endDate: '2025-01-21T18:00:00Z',
    status: 'failed',
    testConditions: [
      { parameter: 'Temperature', value: '25', unit: '°C' },
      { parameter: 'Load Current', value: '10', unit: 'A' }
    ],
    testResults: [
      {
        id: '4',
        parameter: 'Voltage Under Load',
        measuredValue: 22.8,
        unit: 'V',
        specification: '≥ 23.5 V',
        result: 'fail',
        timestamp: '2025-01-21T18:00:00Z'
      }
    ],
    notes: 'Battery failed performance test due to voltage drop under load. Requires investigation.'
  }
];

const getStatusColor = (status: BatteryTest['status']) => {
  switch (status) {
    case 'scheduled':
      return 'bg-blue-100 text-blue-800';
    case 'in_progress':
      return 'bg-yellow-100 text-yellow-800';
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'failed':
      return 'bg-red-100 text-red-800';
    case 'cancelled':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getTestTypeColor = (testType: BatteryTest['testType']) => {
  switch (testType) {
    case 'capacity':
      return 'bg-blue-100 text-blue-800';
    case 'voltage':
      return 'bg-green-100 text-green-800';
    case 'current':
      return 'bg-yellow-100 text-yellow-800';
    case 'cycle_life':
      return 'bg-purple-100 text-purple-800';
    case 'safety':
      return 'bg-red-100 text-red-800';
    case 'environmental':
      return 'bg-orange-100 text-orange-800';
    case 'performance':
      return 'bg-indigo-100 text-indigo-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getResultColor = (result: string) => {
  switch (result) {
    case 'pass':
      return 'bg-green-100 text-green-800';
    case 'fail':
      return 'bg-red-100 text-red-800';
    case 'warning':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const BatteryTesting = () => {
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedTestType, setSelectedTestType] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('tests');

  const statuses = ['all', 'scheduled', 'in_progress', 'completed', 'failed', 'cancelled'];
  const testTypes = ['all', 'capacity', 'voltage', 'current', 'cycle_life', 'safety', 'environmental', 'performance'];

  const filteredTests = batteryTests.filter(test => {
    const matchesSearch = test.testNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         test.batterySku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         test.operator.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || test.status === selectedStatus;
    const matchesTestType = selectedTestType === 'all' || test.testType === selectedTestType;

    return matchesSearch && matchesStatus && matchesTestType;
  });

  // Calculate summary statistics
  const totalTests = batteryTests.length;
  const scheduledTests = batteryTests.filter(t => t.status === 'scheduled').length;
  const inProgressTests = batteryTests.filter(t => t.status === 'in_progress').length;
  const completedTests = batteryTests.filter(t => t.status === 'completed').length;
  const failedTests = batteryTests.filter(t => t.status === 'failed').length;
  const passRate = completedTests > 0 ? Math.round((completedTests / (completedTests + failedTests)) * 100) : 0;

  return (
    <Layout>
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1">Battery Testing</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Comprehensive battery testing protocols and performance validation.
            </p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Schedule Test
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Schedule Battery Test</DialogTitle>
                <DialogDescription>
                  Create a new battery test protocol.
                </DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="battery-id">Battery ID</Label>
                  <Input placeholder="Enter battery ID" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="test-type">Test Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select test type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="capacity">Capacity Test</SelectItem>
                      <SelectItem value="voltage">Voltage Test</SelectItem>
                      <SelectItem value="current">Current Test</SelectItem>
                      <SelectItem value="cycle_life">Cycle Life Test</SelectItem>
                      <SelectItem value="safety">Safety Test</SelectItem>
                      <SelectItem value="environmental">Environmental Test</SelectItem>
                      <SelectItem value="performance">Performance Test</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="test-standard">Test Standard</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select standard" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="IEC 61960">IEC 61960</SelectItem>
                      <SelectItem value="UL 2054">UL 2054</SelectItem>
                      <SelectItem value="UN 38.3">UN 38.3</SelectItem>
                      <SelectItem value="Internal">Internal Standard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="operator">Test Operator</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select operator" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="engineer-a">Test Engineer A</SelectItem>
                      <SelectItem value="engineer-b">Test Engineer B</SelectItem>
                      <SelectItem value="engineer-c">Test Engineer C</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="start-date">Start Date</Label>
                  <Input type="datetime-local" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="batch-id">Batch ID</Label>
                  <Input placeholder="Enter batch ID" />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="notes">Test Notes</Label>
                  <Textarea placeholder="Enter test notes and special instructions..." />
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsCreateDialogOpen(false)}>
                  Schedule Test
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Tests</p>
                <p className="text-2xl font-bold">{totalTests}</p>
              </div>
              <TestTube className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Scheduled</p>
                <p className="text-2xl font-bold text-blue-600">{scheduledTests}</p>
              </div>
              <Clock className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">In Progress</p>
                <p className="text-2xl font-bold text-yellow-600">{inProgressTests}</p>
              </div>
              <Activity className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold text-green-600">{completedTests}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Failed</p>
                <p className="text-2xl font-bold text-red-600">{failedTests}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Pass Rate</p>
                <p className="text-2xl font-bold text-green-600">{passRate}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search tests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status === 'all' ? 'All Status' : status.replace('_', ' ').toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedTestType} onValueChange={setSelectedTestType}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Test Type" />
              </SelectTrigger>
              <SelectContent>
                {testTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {type === 'all' ? 'All Types' : type.replace('_', ' ').toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for different views */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="tests">Battery Tests</TabsTrigger>
          <TabsTrigger value="results">Test Results</TabsTrigger>
        </TabsList>

        <TabsContent value="tests" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Battery Tests ({filteredTests.length})</CardTitle>
              <CardDescription>
                Manage battery testing protocols and track test progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Test Number</TableHead>
                      <TableHead>Battery</TableHead>
                      <TableHead>Test Type</TableHead>
                      <TableHead>Standard</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Operator</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTests.map((test) => (
                      <TableRow key={test.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{test.testNumber}</div>
                            <div className="text-sm text-muted-foreground">{test.batchId}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{test.batteryId}</div>
                            <div className="text-sm text-muted-foreground">{test.batterySku}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getTestTypeColor(test.testType)}>
                            {test.testType.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {test.testStandard}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(test.status)}>
                            {test.status.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{test.operator}</div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(test.startDate).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="icon">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <Edit className="w-4 h-4" />
                            </Button>
                            {test.status === 'scheduled' && (
                              <Button variant="ghost" size="icon" className="text-green-600">
                                <Play className="w-4 h-4" />
                              </Button>
                            )}
                            {test.status === 'in_progress' && (
                              <Button variant="ghost" size="icon" className="text-orange-600">
                                <Pause className="w-4 h-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Test Results Analysis</CardTitle>
              <CardDescription>
                Detailed test results and performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {filteredTests
                  .filter(test => test.testResults && test.testResults.length > 0)
                  .map((test) => (
                    <div key={test.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="font-semibold">{test.testNumber}</h3>
                          <p className="text-sm text-muted-foreground">
                            {test.batterySku} - {test.testType.replace('_', ' ').toUpperCase()}
                          </p>
                        </div>
                        <Badge className={getStatusColor(test.status)}>
                          {test.status.toUpperCase()}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {test.testResults?.map((result) => (
                          <div key={result.id} className="border rounded p-3">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium">{result.parameter}</span>
                              <Badge className={getResultColor(result.result)}>
                                {result.result.toUpperCase()}
                              </Badge>
                            </div>
                            <div className="text-sm space-y-1">
                              <div>
                                <span className="text-muted-foreground">Measured: </span>
                                <span className="font-medium">
                                  {result.measuredValue} {result.unit}
                                </span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Spec: </span>
                                <span>{result.specification}</span>
                              </div>
                              {result.notes && (
                                <div>
                                  <span className="text-muted-foreground">Notes: </span>
                                  <span className="text-sm">{result.notes}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>

                      {test.certification && (
                        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
                          <div className="flex items-center gap-2 mb-2">
                            <Award className="w-4 h-4 text-green-600" />
                            <span className="font-medium text-green-800">Certification</span>
                          </div>
                          <div className="text-sm text-green-700">
                            <div>Certificate: {test.certification.certificateNumber}</div>
                            <div>Standard: {test.certification.standard}</div>
                            <div>Valid until: {new Date(test.certification.expiryDate).toLocaleDateString()}</div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </Layout>
  );
};

export default BatteryTesting;
