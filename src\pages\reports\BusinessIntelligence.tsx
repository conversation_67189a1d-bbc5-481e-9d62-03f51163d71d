import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Factory,
  Users,
  Battery,
  Target,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Download,
  RefreshCw
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

// Mock data for business intelligence
const kpiData = {
  revenue: { value: 2450000, change: 12.5, trend: 'up' },
  production: { value: 15420, change: 8.3, trend: 'up' },
  efficiency: { value: 94.2, change: 2.1, trend: 'up' },
  defectRate: { value: 1.8, change: -0.5, trend: 'down' },
  customerSatisfaction: { value: 4.7, change: 0.2, trend: 'up' },
  employeeCount: { value: 156, change: 3.2, trend: 'up' }
};

const monthlyPerformance = [
  { month: 'Jan', revenue: 180000, production: 1200, efficiency: 92.1, defects: 2.1 },
  { month: 'Feb', revenue: 195000, production: 1350, efficiency: 93.2, defects: 1.9 },
  { month: 'Mar', revenue: 210000, production: 1420, efficiency: 94.1, defects: 1.7 },
  { month: 'Apr', revenue: 225000, production: 1480, efficiency: 94.8, defects: 1.6 },
  { month: 'May', revenue: 240000, production: 1520, efficiency: 95.2, defects: 1.4 },
  { month: 'Jun', revenue: 255000, production: 1580, efficiency: 94.9, defects: 1.8 }
];

const productMix = [
  { name: 'Lithium-Ion', value: 65, revenue: 1592500 },
  { name: 'Lead-Acid', value: 25, revenue: 612500 },
  { name: 'Nickel-Metal', value: 10, revenue: 245000 }
];

const customerSegments = [
  { segment: 'Automotive', revenue: 980000, growth: 15.2 },
  { segment: 'Energy Storage', revenue: 735000, growth: 22.8 },
  { segment: 'Consumer Electronics', revenue: 490000, growth: 8.5 },
  { segment: 'Industrial', revenue: 245000, growth: 12.1 }
];

const operationalMetrics = [
  { metric: 'Production Capacity Utilization', value: 87.5, target: 90, status: 'warning' },
  { metric: 'On-Time Delivery Rate', value: 94.2, target: 95, status: 'warning' },
  { metric: 'Quality Pass Rate', value: 98.2, target: 98, status: 'success' },
  { metric: 'Employee Productivity', value: 112.3, target: 110, status: 'success' },
  { metric: 'Energy Efficiency', value: 89.7, target: 85, status: 'success' },
  { metric: 'Waste Reduction', value: 76.4, target: 80, status: 'warning' }
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const BusinessIntelligence = () => {
  const isMobile = useIsMobile();
  const [selectedPeriod, setSelectedPeriod] = useState('6months');
  const [selectedView, setSelectedView] = useState('overview');

  const periods = [
    { value: '1month', label: 'Last Month' },
    { value: '3months', label: 'Last 3 Months' },
    { value: '6months', label: 'Last 6 Months' },
    { value: '1year', label: 'Last Year' }
  ];

  return (
    <Layout>
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1">
              Business Intelligence
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Comprehensive analytics and insights for battery manufacturing operations.
            </p>
          </div>
          <div className="flex gap-2">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                {periods.map(period => (
                  <SelectItem key={period.value} value={period.value}>
                    {period.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <DollarSign className="w-8 h-8 text-green-600" />
              <div className="flex-1">
                <p className="text-sm text-muted-foreground">Revenue</p>
                <p className="text-2xl font-bold">{formatCurrency(kpiData.revenue.value)}</p>
                <div className="flex items-center gap-1 mt-1">
                  {kpiData.revenue.trend === 'up' ? (
                    <TrendingUp className="w-4 h-4 text-green-600" />
                  ) : (
                    <TrendingDown className="w-4 h-4 text-red-600" />
                  )}
                  <span className={`text-sm ${kpiData.revenue.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                    {kpiData.revenue.change > 0 ? '+' : ''}{kpiData.revenue.change}%
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Factory className="w-8 h-8 text-blue-600" />
              <div className="flex-1">
                <p className="text-sm text-muted-foreground">Production Units</p>
                <p className="text-2xl font-bold">{kpiData.production.value.toLocaleString()}</p>
                <div className="flex items-center gap-1 mt-1">
                  <TrendingUp className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600">
                    +{kpiData.production.change}%
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Target className="w-8 h-8 text-purple-600" />
              <div className="flex-1">
                <p className="text-sm text-muted-foreground">Efficiency</p>
                <p className="text-2xl font-bold">{kpiData.efficiency.value}%</p>
                <div className="flex items-center gap-1 mt-1">
                  <TrendingUp className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600">
                    +{kpiData.efficiency.change}%
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs value={selectedView} onValueChange={setSelectedView} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="operational">Operational</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Monthly Performance */}
            <Card>
              <CardHeader>
                <CardTitle>Monthly Performance Trends</CardTitle>
                <CardDescription>Revenue and production over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={monthlyPerformance}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value, name) => {
                      if (name === 'revenue') return [formatCurrency(Number(value)), 'Revenue'];
                      return [value, name];
                    }} />
                    <Area type="monotone" dataKey="revenue" stackId="1" stroke="#8884d8" fill="#8884d8" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Product Mix */}
            <Card>
              <CardHeader>
                <CardTitle>Product Revenue Mix</CardTitle>
                <CardDescription>Revenue distribution by battery type</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={productMix}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, value }) => `${name}: ${value}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {productMix.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [`${value}%`, name]} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Operational Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Operational Performance Metrics</CardTitle>
              <CardDescription>Key operational indicators vs targets</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {operationalMetrics.map((metric, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">{metric.metric}</h4>
                      {metric.status === 'success' ? (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      ) : (
                        <AlertTriangle className="w-4 h-4 text-yellow-600" />
                      )}
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Current:</span>
                        <span className="font-medium">{metric.value}%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Target:</span>
                        <span className="text-muted-foreground">{metric.target}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${metric.status === 'success' ? 'bg-green-600' : 'bg-yellow-600'}`}
                          style={{ width: `${Math.min((metric.value / metric.target) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Financial Tab */}
        <TabsContent value="financial" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Financial Performance</CardTitle>
              <CardDescription>Revenue and profitability analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={monthlyPerformance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  <Bar dataKey="revenue" fill="#10b981" name="Revenue" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Operational Tab */}
        <TabsContent value="operational" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Production Efficiency</CardTitle>
              <CardDescription>Manufacturing efficiency and quality metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={monthlyPerformance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="efficiency" stroke="#8884d8" name="Efficiency %" />
                  <Line type="monotone" dataKey="defects" stroke="#82ca9d" name="Defect Rate %" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Customers Tab */}
        <TabsContent value="customers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Customer Segments</CardTitle>
              <CardDescription>Revenue and growth by customer segment</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {customerSegments.map((segment, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{segment.segment}</h4>
                      <p className="text-sm text-muted-foreground">{formatCurrency(segment.revenue)}</p>
                    </div>
                    <div className="text-right">
                      <Badge className="bg-green-100 text-green-800">
                        +{segment.growth}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </Layout>
  );
};

export default BusinessIntelligence;
