import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Battery, 
  Search, 
  TrendingUp,
  Edit,
  Eye,
  Truck,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

// Mock data for finished batteries
const finishedBatteries = [
  {
    id: 1,
    model: 'PowerMax 48V 100Ah',
    sku: 'PM-48V-100AH-LI',
    batteryType: 'Lithium-Ion',
    voltage: 48,
    capacity: 100,
    currentStock: 45,
    reservedStock: 12,
    availableStock: 33,
    unitCost: 1250.00,
    sellingPrice: 1875.00,
    location: 'Finished Goods A-1',
    productionDate: '2025-01-10',
    expiryDate: '2027-01-10',
    qualityStatus: 'passed',
    status: 'available'
  },
  {
    id: 2,
    model: 'EcoCell 12V 200Ah',
    sku: 'EC-12V-200AH-LA',
    batteryType: 'Lead-Acid',
    voltage: 12,
    capacity: 200,
    currentStock: 78,
    reservedStock: 25,
    availableStock: 53,
    unitCost: 185.00,
    sellingPrice: 295.00,
    location: 'Finished Goods B-1',
    productionDate: '2025-01-12',
    expiryDate: '2027-01-12',
    qualityStatus: 'passed',
    status: 'available'
  },
  {
    id: 3,
    model: 'FlexPower 24V 150Ah',
    sku: 'FP-24V-150AH-NM',
    batteryType: 'Nickel-Metal Hydride',
    voltage: 24,
    capacity: 150,
    currentStock: 22,
    reservedStock: 8,
    availableStock: 14,
    unitCost: 890.00,
    sellingPrice: 1335.00,
    location: 'Finished Goods C-1',
    productionDate: '2025-01-08',
    expiryDate: '2027-01-08',
    qualityStatus: 'passed',
    status: 'available'
  },
  {
    id: 4,
    model: 'UltraMax 72V 80Ah',
    sku: 'UM-72V-80AH-LI',
    batteryType: 'Lithium-Ion',
    voltage: 72,
    capacity: 80,
    currentStock: 15,
    reservedStock: 15,
    availableStock: 0,
    unitCost: 1680.00,
    sellingPrice: 2520.00,
    location: 'Finished Goods A-2',
    productionDate: '2025-01-14',
    expiryDate: '2027-01-14',
    qualityStatus: 'passed',
    status: 'reserved'
  },
  {
    id: 5,
    model: 'CompactCell 6V 300Ah',
    sku: 'CC-6V-300AH-LA',
    batteryType: 'Lead-Acid',
    voltage: 6,
    capacity: 300,
    currentStock: 95,
    reservedStock: 0,
    availableStock: 95,
    unitCost: 125.00,
    sellingPrice: 199.00,
    location: 'Finished Goods B-2',
    productionDate: '2025-01-13',
    expiryDate: '2027-01-13',
    qualityStatus: 'passed',
    status: 'available'
  },
  {
    id: 6,
    model: 'PowerMax 48V 200Ah',
    sku: 'PM-48V-200AH-LI',
    batteryType: 'Lithium-Ion',
    voltage: 48,
    capacity: 200,
    currentStock: 8,
    reservedStock: 3,
    availableStock: 5,
    unitCost: 2350.00,
    sellingPrice: 3525.00,
    location: 'Finished Goods A-3',
    productionDate: '2025-01-09',
    expiryDate: '2027-01-09',
    qualityStatus: 'testing',
    status: 'testing'
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'available':
      return 'bg-green-100 text-green-800';
    case 'reserved':
      return 'bg-blue-100 text-blue-800';
    case 'testing':
      return 'bg-yellow-100 text-yellow-800';
    case 'shipped':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'available':
      return <CheckCircle size={14} />;
    case 'reserved':
      return <Clock size={14} />;
    case 'testing':
      return <AlertCircle size={14} />;
    case 'shipped':
      return <Truck size={14} />;
    default:
      return null;
  }
};

const getBatteryTypeColor = (type: string) => {
  switch (type) {
    case 'Lithium-Ion':
      return 'bg-blue-100 text-blue-800';
    case 'Lead-Acid':
      return 'bg-green-100 text-green-800';
    case 'Nickel-Metal Hydride':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const FinishedBatteries = () => {
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const batteryTypes = ['all', ...Array.from(new Set(finishedBatteries.map(b => b.batteryType)))];
  const statuses = ['all', 'available', 'reserved', 'testing', 'shipped'];

  const filteredBatteries = finishedBatteries.filter(battery => {
    const matchesSearch = battery.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         battery.sku.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || battery.batteryType === selectedType;
    const matchesStatus = selectedStatus === 'all' || battery.status === selectedStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const totalStock = finishedBatteries.reduce((sum, b) => sum + b.currentStock, 0);
  const totalValue = finishedBatteries.reduce((sum, b) => sum + (b.currentStock * b.unitCost), 0);
  const availableStock = finishedBatteries.reduce((sum, b) => sum + b.availableStock, 0);
  const reservedStock = finishedBatteries.reduce((sum, b) => sum + b.reservedStock, 0);

  return (
    <Layout>
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-1">
          Finished Batteries Inventory
        </h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Manage completed battery products ready for sale and distribution.
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Battery className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Stock</p>
                <p className="text-2xl font-bold">{totalStock}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Available</p>
                <p className="text-2xl font-bold text-green-600">{availableStock}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Clock className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Reserved</p>
                <p className="text-2xl font-bold text-blue-600">{reservedStock}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <TrendingUp className="w-8 h-8 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold">${totalValue.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search by model or SKU..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Battery Type" />
              </SelectTrigger>
              <SelectContent>
                {batteryTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {type === 'all' ? 'All Types' : type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status === 'all' ? 'All Status' : status.charAt(0).toUpperCase() + status.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Batteries Table */}
      <Card>
        <CardHeader>
          <CardTitle>Finished Batteries ({filteredBatteries.length})</CardTitle>
          <CardDescription>
            Complete battery products with specifications and availability
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Specifications</TableHead>
                  <TableHead>Stock Status</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Pricing</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredBatteries.map((battery) => (
                  <TableRow key={battery.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{battery.model}</div>
                        <div className="text-sm text-muted-foreground">
                          SKU: {battery.sku}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getBatteryTypeColor(battery.batteryType)}>
                        {battery.batteryType}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{battery.voltage}V, {battery.capacity}Ah</div>
                        <div className="text-muted-foreground">
                          {(battery.voltage * battery.capacity / 1000).toFixed(1)}kWh
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="font-medium">Total: {battery.currentStock}</div>
                        <div className="text-green-600">Available: {battery.availableStock}</div>
                        {battery.reservedStock > 0 && (
                          <div className="text-blue-600">Reserved: {battery.reservedStock}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(battery.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(battery.status)}
                          {battery.status}
                        </div>
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="font-medium">${battery.sellingPrice}</div>
                        <div className="text-muted-foreground">
                          Cost: ${battery.unitCost}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{battery.location}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </Layout>
  );
};

export default FinishedBatteries;
