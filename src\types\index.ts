// Enhanced types for the admin panel
export interface Product {
  id: string;
  title: string;
  price: number;
  description: string;
  category: string;
  category_id: string;
  main_image: string;
  gallery_images?: string[];
  rating: {
    rate: number;
    count: number;
  };
  // Admin panel specific fields
  affiliate_earning_price: number;
  in_stock: boolean;
  stock_count: number;
  free_shipping: boolean;
  shipping_cost: number;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  name: string;
  image?: string;
  product_count?: number;
  potential_earnings?: number;
  created_at: string;
  updated_at: string;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  address?: string;
  join_date: string;
  total_orders: number;
  total_spent: number;
  status: 'active' | 'inactive';
  order_history?: Order[];
}

export interface AffiliateWorker {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  commission_rate: number;
  total_earnings: number;
  join_date: string;
  status: 'active' | 'inactive';
}

export interface Order {
  id: string;
  customer_id: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  customer_location?: string;
  customer_address?: string;
  affiliate_worker_id?: string;
  affiliate_worker?: AffiliateWorker;
  order_date: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total: number;
  items_count: number;
  payment_method: string;
  shipping_cost: number;
  notes?: string;
  order_items?: OrderItem[];
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  product_name?: string;
  product_image?: string;
  quantity: number;
  price: number;
  affiliate_earnings: number;
}

export interface DeliveryLocation {
  id: string;
  wilaya_code: number;
  wilaya_name: string;
  desk_price: number;
  domicile_price: number;
  enabled: boolean;
}

export interface Payment {
  id: string;
  affiliate_worker_id: string;
  affiliate_worker?: AffiliateWorker;
  amount: number;
  benefit?: number; // Alias for amount for display purposes
  payment_date: string;
  status: 'pending' | 'paid' | 'cancelled';
  payment_method?: string;
  notes?: string;
  user_name?: string; // User name from profiles table
  created_at?: string;
  updated_at?: string;
}

// Dashboard stats interface
export interface DashboardStats {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  conversionRate: number;
  revenueGrowth: number;
  ordersGrowth: number;
  customersGrowth: number;
  conversionGrowth: number;
}

// Chart data interface
export interface ChartData {
  name: string;
  value: number;
  earnings?: number;
  orders?: number;
  revenue?: number;
}

// Buyer interface for dashboard
export interface Buyer {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  recent_purchase: string;
  total_spent: number;
  avatar?: string;
}

// Form interfaces for creating/updating entities
export interface CreateProductForm {
  title: string;
  description: string;
  price: number;
  affiliate_earning_price: number;
  category_id: string;
  main_image: File | string;
  gallery_images?: (File | string)[];
  in_stock: boolean;
  stock_count: number;
  free_shipping: boolean;
  shipping_cost: number;
}

export interface CreateCategoryForm {
  name: string;
  image?: File | string;
}

export interface CreateCustomerForm {
  name: string;
  email: string;
  phone: string;
  location: string;
  address?: string;
}

export interface CreateOrderForm {
  customer_id: string;
  affiliate_worker_id?: string;
  payment_method: string;
  shipping_cost: number;
  notes?: string;
  items: {
    product_id: string;
    quantity: number;
    price: number;
  }[];
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
  total_pages: number;
}

// User Profile types
export interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'manager' | 'affiliate';
  created_at: string;
  updated_at: string;
}

export interface UserSession {
  user: {
    id: string;
    email: string;
  };
  profile: UserProfile;
  joinDate: string; // Formatted join date for display
}

// Search and filter types
export interface SearchFilters {
  query?: string;
  category?: string;
  status?: string;
  date_from?: string;
  date_to?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// Notification types
export interface NotificationSettings {
  new_orders: boolean;
  payments: boolean;
  telegram_user_id?: string;
}

// Location types for warehouse zones
export type ZoneType = 'with_etages' | 'with_parts';

export interface Etage {
  name: string;
  places: number; // Number of places in this étage
  currentStock?: number; // Current stock in this étage
}

export interface ZoneWithEtages {
  id: string;
  type: 'with_etages';
  name: string;
  description?: string;
  etages: Etage[];
}

export interface Part {
  name: string;
  maxCapacity: number;
  currentStock: number;
}

export interface ZoneWithParts {
  id: string;
  type: 'with_parts';
  name: string;
  description?: string;
  parts: Part[];
}

export type Zone = ZoneWithEtages | ZoneWithParts;

// ERP-specific types for battery manufacturing

// Work Orders
export interface WorkOrder {
  id: string;
  workOrderNumber: string;
  productId: string;
  productName: string;
  productSku: string;
  quantity: number;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'draft' | 'scheduled' | 'in_progress' | 'on_hold' | 'completed' | 'cancelled';
  scheduledStartDate: string;
  scheduledEndDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  assignedTeam?: string;
  assignedOperator?: string;
  productionLine?: string;
  instructions: string;
  notes?: string;
  materialRequirements: MaterialRequirement[];
  qualityChecks: QualityCheck[];
  progress: number; // 0-100
  estimatedHours: number;
  actualHours?: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface MaterialRequirement {
  id: string;
  materialId: string;
  materialName: string;
  requiredQuantity: number;
  allocatedQuantity: number;
  unit: string;
  status: 'pending' | 'allocated' | 'consumed';
}

// Quality Assurance
export interface QualityCheck {
  id: string;
  workOrderId?: string;
  batchId?: string;
  checkpointName: string;
  checkpointType: 'incoming' | 'in_process' | 'final' | 'outgoing';
  inspector: string;
  scheduledDate: string;
  completedDate?: string;
  status: 'pending' | 'in_progress' | 'passed' | 'failed' | 'on_hold';
  criteria: QualityCriteria[];
  results?: QualityResult[];
  notes?: string;
  corrective_actions?: CorrectiveAction[];
}

export interface QualityCriteria {
  id: string;
  parameter: string;
  specification: string;
  tolerance: string;
  testMethod: string;
  required: boolean;
}

export interface QualityResult {
  id: string;
  criteriaId: string;
  measuredValue: string;
  result: 'pass' | 'fail' | 'warning';
  notes?: string;
}

export interface CorrectiveAction {
  id: string;
  description: string;
  assignedTo: string;
  dueDate: string;
  status: 'open' | 'in_progress' | 'completed' | 'cancelled';
  completedDate?: string;
}

export interface NonConformance {
  id: string;
  ncNumber: string;
  workOrderId?: string;
  batchId?: string;
  productId: string;
  description: string;
  severity: 'minor' | 'major' | 'critical';
  category: 'material' | 'process' | 'equipment' | 'documentation' | 'other';
  detectedBy: string;
  detectedDate: string;
  status: 'open' | 'investigating' | 'corrective_action' | 'closed';
  rootCause?: string;
  correctiveActions: CorrectiveAction[];
  preventiveActions?: string;
  closedBy?: string;
  closedDate?: string;
}

// Battery Testing
export interface BatteryTest {
  id: string;
  testNumber: string;
  batteryId: string;
  batterySku: string;
  batchId: string;
  testType: 'capacity' | 'voltage' | 'current' | 'cycle_life' | 'safety' | 'environmental' | 'performance';
  testProtocol: string;
  testStandard: string; // e.g., IEC 61960, UL 2054
  operator: string;
  startDate: string;
  endDate?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  testConditions: TestCondition[];
  testResults: TestResult[];
  certification?: CertificationInfo;
  notes?: string;
}

export interface TestCondition {
  parameter: string;
  value: string;
  unit: string;
}

export interface TestResult {
  id: string;
  parameter: string;
  measuredValue: number;
  unit: string;
  specification: string;
  result: 'pass' | 'fail' | 'warning';
  timestamp: string;
}

export interface CertificationInfo {
  certificateNumber: string;
  issuedBy: string;
  issuedDate: string;
  expiryDate: string;
  standard: string;
  status: 'valid' | 'expired' | 'revoked';
}

// Enhanced Customer Management
export interface CustomerContact {
  id: string;
  customerId: string;
  name: string;
  title: string;
  email: string;
  phone: string;
  department: string;
  isPrimary: boolean;
}

export interface CustomerCredit {
  customerId: string;
  creditLimit: number;
  currentBalance: number;
  availableCredit: number;
  paymentTerms: string; // e.g., "Net 30", "COD"
  creditRating: 'excellent' | 'good' | 'fair' | 'poor';
  lastCreditReview: string;
}

export interface CustomerContract {
  id: string;
  customerId: string;
  contractNumber: string;
  contractType: 'supply' | 'service' | 'maintenance' | 'framework';
  startDate: string;
  endDate: string;
  value: number;
  currency: string;
  status: 'draft' | 'active' | 'expired' | 'terminated';
  terms: string;
}

// Enhanced Sales Orders
export interface SalesOrder {
  id: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  customerContact: string;
  orderDate: string;
  requestedDeliveryDate: string;
  confirmedDeliveryDate?: string;
  status: 'draft' | 'confirmed' | 'in_production' | 'ready_to_ship' | 'shipped' | 'delivered' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  salesRep: string;
  orderItems: SalesOrderItem[];
  subtotal: number;
  taxAmount: number;
  shippingCost: number;
  totalAmount: number;
  currency: string;
  paymentTerms: string;
  shippingAddress: Address;
  billingAddress: Address;
  notes?: string;
  internalNotes?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface SalesOrderItem {
  id: string;
  salesOrderId: string;
  productId: string;
  productName: string;
  productSku: string;
  quantity: number;
  unitPrice: number;
  discount: number;
  lineTotal: number;
  requestedDeliveryDate: string;
  status: 'pending' | 'confirmed' | 'in_production' | 'ready' | 'shipped' | 'delivered';
  workOrderId?: string;
  notes?: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

// Quotations
export interface Quotation {
  id: string;
  quoteNumber: string;
  customerId: string;
  customerName: string;
  contactPerson: string;
  quoteDate: string;
  validUntil: string;
  status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired' | 'converted';
  salesRep: string;
  quoteItems: QuotationItem[];
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  currency: string;
  paymentTerms: string;
  deliveryTerms: string;
  notes?: string;
  convertedToOrderId?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface QuotationItem {
  id: string;
  quotationId: string;
  productId: string;
  productName: string;
  productSku: string;
  description: string;
  quantity: number;
  unitPrice: number;
  discount: number;
  lineTotal: number;
  leadTime: string;
  notes?: string;
}
